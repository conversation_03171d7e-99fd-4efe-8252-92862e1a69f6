# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Repository Overview

This is a comprehensive MATLAB implementation of a HUD (Head-Up Display) optical pre-compensation system. The system corrects spatially-varying optical blur (astigmatism, coma, spherical aberration) in HUD optics by generating pre-compensated images that appear sharp when viewed through the actual HUD optical system.

## Key Architecture

### Two-Stage Workflow
- **Offline Stage**: One-time calibration that generates pre-compensation kernel maps from BSF (Beam Spread Function) simulation data
- **Online Stage**: Real-time application of pre-compensation to UI images using pre-computed kernel maps

### Core Algorithm Components
- **BSF to PSF Conversion**: Complete Wiener deconvolution implementation that reverses optical simulation data to obtain Point Spread Functions
- **Energy-Preserving Downsampling**: Mathematically correct energy conservation using binning/summation with error control <1e-10
- **Spatial Interpolation**: "Convolve-first, interpolate-later" strategy using `scatteredInterpolant` for smooth spatial variation
- **Spatially-Varying Deconvolution**: Handles optical aberrations that vary across the field of view

## Essential Commands

### Main Workflow
```matlab
main_workflow  % Complete end-to-end processing
```

### Testing and Validation
```matlab
test_system             % Comprehensive test suite
test_speos_large_file   % Large file processing tests
verify_implementation   % Implementation verification
```

### Data Generation
```matlab
create_sample_data      % Generate sample BSF data (MATLAB)
```
```bash
python3 create_sample_data.py      # Generate sample data (Python)
python3 create_large_speos_sample.py  # Generate large SPEOS test data
```

### Syntax Validation
```bash
python3 test_matlab_syntax.py  # Pre-flight syntax check
```

## Core Functions

### Main Functions
- `offline_stage_auto()` - Located in `offline_calculate_all_prekernels.m`
- `online_stage_auto()` - Located in `online_apply_compensation.m`
- `helper_parse_speos_data()` - Located in `helper_parse_speos_data.m`
- `helper_downsample_kernel()` - Located in `helper_downsample_kernel.m`

### Internal Functions
- `deconvolve_wiener()` - Complete Wiener deconvolution implementation
- `parse_mat_files()`, `parse_text_file()`, `parse_speos_large_file()` - Data parsing functions

## Data Format Support

### Primary Format (Recommended)
- Individual `.mat` files: `bsf_1.mat` to `bsf_36.mat` in `bsf_data/` directory
- Each file contains `bsf_data` variable with 31×31 BSF matrix

### Alternative Formats
- Small text files with X,Y,Intensity columns
- Large SPEOS export files (>100MB) with automatic enhanced processing mode

### Auto-Detection Logic
The system automatically detects file formats and sizes:
- Files >100MB trigger enhanced processing with memory optimization
- Directory vs file detection determines parsing strategy
- Coordinate files are automatically loaded when available

## Configuration Parameters

### Key Parameters in `main_workflow.m`
- `grid_dims = [6, 6]` - Sampling grid dimensions
- `oversampling_rate = 3` - Simulation to UI resolution ratio
- `matrix_size = 31` - BSF matrix size
- `noise_ratio_step1 = 1e-5` - BSF→PSF regularization
- `noise_ratio_step2 = 1e-3` - PSF→PreKernel regularization

### Critical Configuration
- `grid_centers_highres_coords` - 36×2 matrix of actual simulation coordinates
- **Must be replaced with real coordinates from your optical simulation**

## Advanced Features

### Large File Processing
- Automatic detection and memory-optimized processing for files >100MB
- Chunked reading with intelligent region segmentation
- High-precision coordinate calculation using intensity-weighted centroids
- Comprehensive quality assessment and reporting

### Parallel Computing
- Automatic detection of Parallel Computing Toolbox
- Graceful fallback to serial processing when toolbox unavailable
- Multi-core acceleration for kernel processing

### Quality Assurance
- Comprehensive input validation for all functions
- Energy conservation verification (error <1e-10)
- Coordinate system consistency checks
- Mathematical correctness validation

## Output Files

### Generated Files
- `prekernel_map_lowres_final.mat` - Pre-compensation kernel map (reusable)
- `precompensated_image_final.png` - Pre-compensated output image
- Quality reports and processing statistics

### Important Note
Pre-compensated images will appear blurred/distorted on computer screens - this is normal and expected. They are designed to appear sharp when viewed through the actual HUD optical system.

## Environment Requirements

### MATLAB Requirements
- MATLAB R2020a or newer (R2022a+ recommended)
- **Required**: Image Processing Toolbox
- **Optional**: Parallel Computing Toolbox (for acceleration)

### Python Requirements (for data generation only)
- Python 3.7+
- numpy, scipy, matplotlib, pillow

## Common Issues and Solutions

### Data Source Validation
- System validates data source existence before processing
- Automatically creates sample data if none exists
- Provides clear error messages for missing files or incorrect formats

### Memory Management
- Large files (>100MB) automatically trigger memory-optimized processing
- Chunked reading prevents memory overflow
- Progress reporting for long-running operations

## Testing Strategy

### Comprehensive Test Coverage
- `test_system.m` - End-to-end workflow validation
- Input validation for all functions
- Error handling and recovery testing
- Mathematical correctness verification
- Coordinate system transformation testing

### Performance Benchmarks
- Offline stage: ~30 seconds for 36 kernels
- Online stage: ~1-2 seconds for typical UI images
- Memory usage optimized for typical HUD image sizes

## Development Notes

### Code Style
- Extensive input validation and error handling
- Comprehensive documentation and comments
- Vectorized operations for performance
- try-catch blocks for robust error recovery

### Algorithm Implementation
- Complete Wiener deconvolution with proper regularization (fully implemented)
- Energy-preserving downsampling using binning/summation
- Spatial interpolation using scattered interpolation
- Support for both regular and irregular grids
- All core algorithms are fully implemented and tested