# 核心计算依赖
numpy>=1.21.0
scipy>=1.7.0
scikit-image>=0.18.0
matplotlib>=3.5.0

# 可选性能加速
numba>=0.54.0
opencv-python>=4.5.0

# 数据处理
pandas>=1.3.0
h5py>=3.1.0

# 开发和测试工具
pytest>=6.0.0
pytest-cov>=2.12.0
black>=21.0.0
flake8>=3.9.0
mypy>=0.910
isort>=5.9.0

# 文档生成
sphinx>=4.0.0
sphinx-rtd-theme>=0.5.0

# 项目打包
setuptools>=50.0.0
wheel>=0.36.0
twine>=3.4.0

# Jupyter支持（可选）
jupyter>=1.0.0
ipywidgets>=7.6.0

# 性能分析（可选）
memory-profiler>=0.58.0
line-profiler>=3.3.0 