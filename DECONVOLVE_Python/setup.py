#!/usr/bin/env python3
"""
HUD光学预补偿系统 - Python版本
从MATLAB代码完全重写的高性能图像预补偿库
"""

from setuptools import setup, find_packages
import os

# 读取README文件
def read_readme():
    with open("README.md", "r", encoding="utf-8") as fh:
        return fh.read()

# 读取requirements.txt
def read_requirements():
    with open("requirements.txt", "r", encoding="utf-8") as fh:
        return [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="deconvolve",
    version="1.0.0",
    author="HUD PreCompensation Team",
    author_email="<EMAIL>",
    description="HUD光学预补偿系统 - 高性能Python实现",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/your-org/deconvolve-python",
    project_urls={
        "Bug Tracker": "https://github.com/your-org/deconvolve-python/issues",
        "Documentation": "https://deconvolve-python.readthedocs.io/",
        "Source Code": "https://github.com/your-org/deconvolve-python",
    },
    
    # 包配置
    package_dir={"": "src"},
    packages=find_packages(where="src"),
    
    # 依赖配置
    python_requires=">=3.8",
    install_requires=[
        "numpy>=1.21.0",
        "scipy>=1.7.0", 
        "scikit-image>=0.18.0",
        "matplotlib>=3.5.0",
    ],
    
    extras_require={
        "acceleration": [
            "numba>=0.54.0",
            "opencv-python>=4.5.0",
        ],
        "data": [
            "pandas>=1.3.0",
            "h5py>=3.1.0",
        ],
        "dev": [
            "pytest>=6.0.0",
            "pytest-cov>=2.12.0",
            "black>=21.0.0",
            "flake8>=3.9.0",
            "mypy>=0.910",
            "isort>=5.9.0",
        ],
        "docs": [
            "sphinx>=4.0.0",
            "sphinx-rtd-theme>=0.5.0",
        ],
        "jupyter": [
            "jupyter>=1.0.0",
            "ipywidgets>=7.6.0",
        ],
        "profiling": [
            "memory-profiler>=0.58.0",
            "line-profiler>=3.3.0",
        ],
    },
    
    # 分类信息
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Science/Research",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Scientific/Engineering :: Image Processing",
        "Topic :: Scientific/Engineering :: Physics",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    
    # 关键词
    keywords="image-processing, deconvolution, hud, optics, compensation, computer-vision",
    
    # 包含数据文件
    include_package_data=True,
    package_data={
        "deconvolve": ["data/*.json", "data/*.yaml"],
    },
    
    # 命令行工具 (暂时禁用，待scripts目录实现)
    # entry_points={
    #     "console_scripts": [
    #         "deconvolve-workflow=deconvolve.scripts.main_workflow:main",
    #         "deconvolve-benchmark=deconvolve.scripts.benchmark:main", 
    #         "deconvolve-create-data=deconvolve.scripts.create_sample_data:main",
    #     ],
    # },
    
    # 项目状态
    zip_safe=False,
) 