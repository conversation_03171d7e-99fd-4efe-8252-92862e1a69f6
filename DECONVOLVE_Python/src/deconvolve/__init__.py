"""
HUD光学预补偿系统 - Python版本

这是一个高性能的图像预补偿库，用于抵消HUD光学系统引入的模糊和畸变。
从MATLAB代码完全重写，提供更好的性能和可维护性。

主要模块:
- core: 核心算法模块
- io: 数据输入输出模块  
- workflow: 工作流模块
- utils: 工具函数模块
"""

__version__ = "1.0.0"
__author__ = "HUD PreCompensation Team"
__email__ = "<EMAIL>"

# 导入主要接口 - 修复导入路径
from .workflow.offline_stage import generate_prekernel_map
from .workflow.online_stage import apply_compensation
from .io.data_io import load_bsf_data, DataIO
from .core.wiener_deconv import wiener_deconvolve

# 为了保持兼容性，创建parse_speos_data快捷方式
def parse_speos_data(data_source, grid_centers_coords, matrix_size):
    """
    解析SPEOS数据的便捷函数
    """
    data_io = DataIO()
    return data_io.parse_speos_data(data_source, grid_centers_coords, matrix_size)

__all__ = [
    "generate_prekernel_map",
    "apply_compensation", 
    "load_bsf_data",
    "parse_speos_data",
    "wiener_deconvolve",
    "DataIO",
    "__version__",
] 