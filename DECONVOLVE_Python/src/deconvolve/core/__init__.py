"""
核心算法模块

包含HUD光学预补偿系统的核心算法实现:
- wiener_deconv: Wiener反卷积算法
- interpolation: 插值和重采样算法
- image_processing: 图像处理工具
"""

# 导入核心类
from .wiener_deconv import WienerDeconvolution, wiener_deconvolve
from .interpolation import InterpolationEngine, ScatteredInterpolant
from .image_processing import ImageProcessor

# 导入便捷函数
from .interpolation import matlab_griddata, scattered_interpolate, spatial_interpolate_images

__all__ = [
    "WienerDeconvolution",
    "wiener_deconvolve", 
    "InterpolationEngine",
    "ScatteredInterpolant",
    "ImageProcessor",
    "matlab_griddata",
    "scattered_interpolate", 
    "spatial_interpolate_images",
] 