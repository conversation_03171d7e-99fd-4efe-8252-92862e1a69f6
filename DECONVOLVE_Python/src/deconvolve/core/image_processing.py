"""
图像处理工具模块

完全移植自MATLAB helper_downsample_kernel.m和相关图像处理功能。
提供能量守恒的图像重采样、滤波和验证工具。

主要功能：
- 能量守恒的核下采样
- 图像重采样和插值
- 图像填充和归一化
- 质量验证工具
"""

import numpy as np
from typing import Tuple, Union
import warnings
from scipy import ndimage
from skimage import transform


class ImageProcessor:
    """
    图像处理器类
    
    提供各种图像处理功能，特别是能量守恒的下采样算法
    """
    
    def __init__(self):
        """初始化图像处理器"""
        pass
    
    def downsample_kernel_energy_preserving(self, high_res_kernel: np.ndarray, 
                                          scale_factor: int) -> np.ndarray:
        """
        能量守恒的核下采样 - 完全移植自MATLAB helper_downsample_kernel.m
        
        使用能量守恒的分箱（binning）方法将高分辨率核转换为低分辨率核。
        确保核的总能量在下采样过程中得到保持。
        
        参数:
            high_res_kernel: 高分辨率核（2D数值矩阵）
            scale_factor: 下采样因子（正整数）
        
        返回:
            下采样后的核，保持能量守恒
        
        引发:
            ValueError: 当输入参数无效时
        """
        # 输入验证 - 与MATLAB版本一致
        if not isinstance(high_res_kernel, np.ndarray) or high_res_kernel.ndim != 2:
            raise ValueError("high_res_kernel必须是2D数值矩阵")
        
        if not isinstance(scale_factor, int) or scale_factor <= 0:
            raise ValueError("scale_factor必须是正整数")
    
        if np.any(np.isnan(high_res_kernel)) or np.any(np.isinf(high_res_kernel)):
            raise ValueError("high_res_kernel包含NaN或Inf值")
        
        hr_h, hr_w = high_res_kernel.shape
    
        if hr_h < scale_factor or hr_w < scale_factor:
            raise ValueError(f"核尺寸({hr_h}x{hr_w})必须至少与scale_factor({scale_factor})一样大")
    
        # 存储原始能量用于验证 - 与MATLAB版本一致
        original_energy = np.sum(high_res_kernel)
    
        # 如果尺寸不能被scale_factor整除，则用零填充 - 与MATLAB版本一致
        pad_h = hr_h % scale_factor
        pad_w = hr_w % scale_factor
        if pad_h != 0:
            pad_h = scale_factor - pad_h
        if pad_w != 0:
            pad_w = scale_factor - pad_w
    
        if pad_h > 0 or pad_w > 0:
            try:
                # 与MATLAB的padarray(matrix, [pad_h, pad_w], 0, 'post')一致
                high_res_kernel = np.pad(
                    high_res_kernel,
                    ((0, pad_h), (0, pad_w)),
                    mode='constant',
                    constant_values=0
                )
            except Exception as e:
                raise ValueError(f"填充核失败: {e}")
    
        hr_h, hr_w = high_res_kernel.shape
        lr_h = hr_h // scale_factor
        lr_w = hr_w // scale_factor
    
        # 验证尺寸现在是兼容的
        if hr_h % scale_factor != 0 or hr_w % scale_factor != 0:
            raise ValueError("内部错误：填充后的尺寸不能被scale_factor整除")
        
        try:
            # 能量守恒分箱使用向量化操作 - 与MATLAB版本完全一致
            # 步骤1：重新整形并沿高度维度求和
            reshaped_h = high_res_kernel.reshape(lr_h, scale_factor, hr_w)
            sum_h = np.sum(reshaped_h, axis=1)
            
            # 步骤2：重新整形并沿宽度维度求和
            reshaped_w = sum_h.reshape(lr_h, lr_w, scale_factor)
            low_res_kernel = np.sum(reshaped_w, axis=2)
            
        except Exception as e:
            raise ValueError(f"分箱操作失败: {e}")
    
        # 验证结果尺寸
        if low_res_kernel.shape != (lr_h, lr_w):
            raise ValueError("内部错误：输出核尺寸不正确")
        
        # 能量守恒检查 - 与MATLAB版本一致
        downsampled_energy = np.sum(low_res_kernel)
        energy_error = abs(downsampled_energy - original_energy) / max(abs(original_energy), np.finfo(float).eps)
    
        if energy_error > 1e-10:
            warnings.warn(f"能量守恒误差: {energy_error*100:.2e}% 下采样过程中的能量变化")
        
        # 归一化核以在卷积后保持图像亮度 - 与MATLAB版本一致
        kernel_sum = np.sum(low_res_kernel)
        if abs(kernel_sum) > 1e-12:
            low_res_kernel = low_res_kernel / kernel_sum
        else:
            warnings.warn("核的和接近零。使用均匀分布。")
            low_res_kernel = np.ones((lr_h, lr_w)) / (lr_h * lr_w)
        
        # 最终验证
        if np.any(np.isnan(low_res_kernel)) or np.any(np.isinf(low_res_kernel)):
            raise ValueError("输出核包含NaN或Inf值")
    
        return low_res_kernel
    
    def resize_image_energy_preserving(self, image: np.ndarray, 
                                     target_size: Tuple[int, int],
                                     method: str = 'bilinear') -> np.ndarray:
        """
        能量守恒的图像重采样
        
        参数:
            image: 输入图像
            target_size: 目标尺寸 (height, width)
            method: 插值方法 ('bilinear', 'bicubic', 'nearest')
            
        返回:
            重采样后的图像
        """
        if not isinstance(image, np.ndarray):
            raise ValueError("image必须是numpy数组")
        
        if len(target_size) != 2 or any(s <= 0 for s in target_size):
            raise ValueError("target_size必须是两个正整数的元组")
        
        # 保存原始能量
        original_energy = np.sum(image)
        
        # 使用scikit-image进行重采样
        if method == 'bilinear':
            order = 1
        elif method == 'bicubic':
            order = 3
        elif method == 'nearest':
            order = 0
        else:
            raise ValueError(f"不支持的插值方法: {method}")
        
        # 重采样
        resized = transform.resize(
            image, 
            target_size, 
            order=order, 
            preserve_range=True,
            anti_aliasing=True
        )
        
        # 能量守恒校正
        resized_energy = np.sum(resized)
        if resized_energy > np.finfo(float).eps:
            energy_ratio = original_energy / resized_energy
            resized = resized * energy_ratio
        
        return resized
    
    def apply_filter(self, image: np.ndarray, filter_type: str = 'gaussian',
                     **kwargs) -> np.ndarray:
        """
        应用各种滤波器
        
        参数:
            image: 输入图像
            filter_type: 滤波器类型 ('gaussian', 'median', 'uniform')
            **kwargs: 滤波器参数
            
        返回:
            滤波后的图像
        """
        if filter_type == 'gaussian':
            sigma = kwargs.get('sigma', 1.0)
            return ndimage.gaussian_filter(image, sigma=sigma)
        
        elif filter_type == 'median':
            size = kwargs.get('size', 3)
            return ndimage.median_filter(image, size=size)
        
        elif filter_type == 'uniform':
            size = kwargs.get('size', 3)
            return ndimage.uniform_filter(image, size=size)
        
        else:
            raise ValueError(f"不支持的滤波器类型: {filter_type}")
    
    def pad_image(self, image: np.ndarray, pad_size: Union[int, Tuple], 
                  mode: str = 'constant', **kwargs) -> np.ndarray:
        """
        图像填充 - 与MATLAB padarray类似
        
        参数:
            image: 输入图像
            pad_size: 填充大小
            mode: 填充模式 ('constant', 'edge', 'reflect', 'symmetric')
            **kwargs: 其他参数（如constant_values）
            
        返回:
            填充后的图像
        """
        if isinstance(pad_size, int):
            pad_width = pad_size
        elif isinstance(pad_size, (tuple, list)):
            if len(pad_size) == 2:
                pad_width = pad_size
            else:
                raise ValueError("pad_size必须是整数或长度为2的元组")
        else:
            raise ValueError("pad_size类型无效")
        
        return np.pad(image, pad_width, mode=mode, **kwargs)
    
    def normalize_image(self, image: np.ndarray, 
                       target_range: Tuple[float, float] = (0.0, 1.0)) -> np.ndarray:
        """
        图像归一化
        
        参数:
            image: 输入图像
            target_range: 目标范围
            
        返回:
            归一化后的图像
        """
        img_min, img_max = np.min(image), np.max(image)
        
        if img_max - img_min < np.finfo(float).eps:
            # 处理常数图像
            return np.full_like(image, target_range[0])
        
        # 线性缩放到目标范围
        normalized = (image - img_min) / (img_max - img_min)
        normalized = normalized * (target_range[1] - target_range[0]) + target_range[0]
        
        return normalized
    
    def validate_image_energy_conservation(self, original: np.ndarray, 
                                         processed: np.ndarray,
                                         tolerance: float = 1e-10) -> Tuple[bool, float]:
        """
        验证图像处理过程中的能量守恒
        
        参数:
            original: 原始图像
            processed: 处理后的图像
            tolerance: 容差
            
        返回:
            (是否守恒, 能量误差比率)
        """
        original_energy = np.sum(original)
        processed_energy = np.sum(processed)
        
        if abs(original_energy) < np.finfo(float).eps:
            return True, 0.0  # 零能量情况
        
        energy_error = abs(processed_energy - original_energy) / abs(original_energy)
        is_conserved = energy_error < tolerance
        
        return is_conserved, energy_error


# 便捷函数
def downsample_kernel(high_res_kernel: np.ndarray, scale_factor: int) -> np.ndarray:
    """
    便捷函数：能量守恒的核下采样
    
    与MATLAB函数helper_downsample_kernel具有相同的接口。
    """
    processor = ImageProcessor()
    return processor.downsample_kernel_energy_preserving(high_res_kernel, scale_factor)


def resize_image(image: np.ndarray, target_size: Tuple[int, int], 
                method: str = 'bilinear') -> np.ndarray:
    """
    便捷函数：图像重采样
    """
    processor = ImageProcessor()
    return processor.resize_image_energy_preserving(image, target_size, method)


def apply_filter(
    image: np.ndarray,
    kernel: np.ndarray,
    mode: str = 'reflect',
    boundary: str = 'same'
) -> np.ndarray:
    """
    对图像应用滤波器
    
    使用给定的核对图像进行卷积滤波。
    
    参数:
        image: 输入图像，2D numpy数组
        kernel: 滤波核，2D numpy数组
        mode: 边界处理模式 ('reflect', 'constant', 'nearest', 'mirror', 'wrap')
        boundary: 输出尺寸 ('same', 'valid', 'full')
        
    返回:
        滤波后的图像
    """
    if not isinstance(image, np.ndarray) or image.ndim != 2:
        raise ValueError("image必须是2D numpy数组")
    if not isinstance(kernel, np.ndarray) or kernel.ndim != 2:
        raise ValueError("kernel必须是2D numpy数组")
    
    # 使用scipy.ndimage进行卷积
    if boundary == 'same':
        # 使用convolve保持输出尺寸与输入相同
        result = ndimage.convolve(image, kernel, mode=mode)
    elif boundary == 'valid':
        # 只计算完全重叠的区域
        result = ndimage.convolve(image, kernel, mode='constant', cval=0.0)
        # 裁剪到valid区域
        kh, kw = kernel.shape
        pad_h, pad_w = kh // 2, kw // 2
        if pad_h > 0 and pad_w > 0:
            result = result[pad_h:-pad_h, pad_w:-pad_w]
    else:  # 'full'
        # 完整卷积，输出尺寸更大
        result = ndimage.convolve(image, kernel, mode='constant', cval=0.0)
    
    return result


def normalize_image(
    image: np.ndarray,
    method: str = 'minmax',
    target_range: Tuple[float, float] = (0.0, 1.0)
) -> np.ndarray:
    """
    图像归一化
    
    参数:
        image: 输入图像
        method: 归一化方法 ('minmax', 'zscore', 'sum')
        target_range: 目标范围 (min, max)
        
    返回:
        归一化后的图像
    """
    image = image.astype(np.float64)
    
    if method == 'minmax':
        # 最小-最大归一化
        img_min, img_max = np.min(image), np.max(image)
        if img_max > img_min:
            image = (image - img_min) / (img_max - img_min)
        else:
            image = np.zeros_like(image)
        
        # 缩放到目标范围
        image = image * (target_range[1] - target_range[0]) + target_range[0]
        
    elif method == 'zscore':
        # Z-score标准化
        mean, std = np.mean(image), np.std(image)
        if std > 0:
            image = (image - mean) / std
        else:
            image = image - mean
            
    elif method == 'sum':
        # 和归一化（用于概率分布）
        total = np.sum(image)
        if total > 0:
            image = image / total
        else:
            warnings.warn("图像总和为零，无法进行和归一化")
            
    else:
        raise ValueError(f"不支持的归一化方法: {method}")
    
    return image


def pad_to_size(
    image: np.ndarray,
    target_shape: Tuple[int, int],
    mode: str = 'constant',
    constant_values: float = 0.0
) -> np.ndarray:
    """
    将图像填充到指定尺寸
    
    参数:
        image: 输入图像
        target_shape: 目标尺寸 (height, width)
        mode: 填充模式
        constant_values: 常数填充值
        
    返回:
        填充后的图像
    """
    current_shape = image.shape
    target_h, target_w = target_shape
    
    if current_shape[0] > target_h or current_shape[1] > target_w:
        raise ValueError("目标尺寸不能小于当前尺寸")
    
    pad_h = target_h - current_shape[0]
    pad_w = target_w - current_shape[1]
    
    # 计算对称填充
    pad_top = pad_h // 2
    pad_bottom = pad_h - pad_top
    pad_left = pad_w // 2
    pad_right = pad_w - pad_left
    
    padded = np.pad(
        image,
        ((pad_top, pad_bottom), (pad_left, pad_right)),
        mode=mode,
        constant_values=constant_values
    )
    
    return padded


def calculate_image_metrics(
    image1: np.ndarray,
    image2: np.ndarray
) -> dict:
    """
    计算两幅图像之间的相似性指标
    
    参数:
        image1: 第一幅图像
        image2: 第二幅图像
        
    返回:
        包含各种指标的字典
    """
    if image1.shape != image2.shape:
        raise ValueError("两幅图像尺寸必须相同")
    
    metrics = {}
    
    # 均方误差 (MSE)
    mse = np.mean((image1 - image2) ** 2)
    metrics['mse'] = mse
    
    # 峰值信噪比 (PSNR)
    if mse > 0:
        max_val = max(np.max(image1), np.max(image2))
        psnr = 20 * np.log10(max_val / np.sqrt(mse))
    else:
        psnr = float('inf')
    metrics['psnr'] = psnr
    
    # 相关系数
    corr = np.corrcoef(image1.flatten(), image2.flatten())[0, 1]
    metrics['correlation'] = corr if not np.isnan(corr) else 0.0
    
    # 结构相似性指数的简化版本
    mean1, mean2 = np.mean(image1), np.mean(image2)
    var1, var2 = np.var(image1), np.var(image2)
    cov = np.mean((image1 - mean1) * (image2 - mean2))
    
    # 简化的SSIM
    c1, c2 = 0.01, 0.03
    ssim = ((2 * mean1 * mean2 + c1) * (2 * cov + c2)) / \
           ((mean1**2 + mean2**2 + c1) * (var1 + var2 + c2))
    metrics['ssim'] = ssim
    
    return metrics


def create_gaussian_kernel(
    size: int,
    sigma: float
) -> np.ndarray:
    """
    创建高斯核
    
    参数:
        size: 核大小（奇数）
        sigma: 标准差
        
    返回:
        归一化的高斯核
    """
    if size % 2 == 0:
        raise ValueError("核大小必须是奇数")
    
    center = size // 2
    kernel = np.zeros((size, size))
    
    for i in range(size):
        for j in range(size):
            x, y = i - center, j - center
            kernel[i, j] = np.exp(-(x**2 + y**2) / (2 * sigma**2))
    
    # 归一化
    kernel = kernel / np.sum(kernel)
    
    return kernel 