"""
插值算法模块

实现各种插值算法，特别是空间变化插值和scattered data插值。
这些算法用于在线阶段进行空间变化的图像补偿。

主要功能：
- Scattered data插值（移植自MATLAB scatteredInterpolant）
- 空间变化图像插值
- 多种插值方法支持
"""

import numpy as np
from scipy.interpolate import griddata, LinearNDInterpolator, NearestNDInterpolator
from scipy.interpolate import RegularGridInterpolator, RectBivariateSpline
from scipy.spatial import cKDTree
from typing import Tuple, Union, Optional, Literal, List
import warnings

def matlab_griddata(x: np.ndarray, y: np.ndarray, v: np.ndarray, 
                   xi: np.ndarray, yi: np.ndarray, 
                   method: Literal['linear', 'nearest', 'cubic'] = 'linear') -> np.ndarray:
    """
    MATLAB griddata函数的Python实现
    
    参数:
        x, y: 已知数据点的坐标
        v: 已知数据点的值
        xi, yi: 插值目标点的坐标网格
        method: 插值方法 ('linear', 'nearest', 'cubic')
    
    返回:
        插值结果数组
    """
    # 验证输入
    x, y, v = np.asarray(x), np.asarray(y), np.asarray(v)
    xi, yi = np.asarray(xi), np.asarray(yi)
    
    if x.shape != y.shape or x.shape != v.shape:
        raise ValueError("x, y, v必须具有相同的形状")
    
    # 移除NaN值
    valid_mask = ~(np.isnan(x) | np.isnan(y) | np.isnan(v))
    if not np.any(valid_mask):
        raise ValueError("没有有效的数据点")
    
    x_clean = x[valid_mask]
    y_clean = y[valid_mask]
    v_clean = v[valid_mask]
    
    # 创建插值点数组
    points = np.column_stack((x_clean, y_clean))
    xi_flat = xi.ravel()
    yi_flat = yi.ravel()
    points_i = np.column_stack((xi_flat, yi_flat))
    
    # 执行插值
    try:
        result = griddata(points, v_clean, points_i, method=method, fill_value=np.nan)
        return result.reshape(xi.shape)
    except Exception as e:
        warnings.warn(f"插值失败，使用最近邻方法: {e}")
        result = griddata(points, v_clean, points_i, method='nearest', fill_value=np.nan)
        return result.reshape(xi.shape)

class ScatteredInterpolant:
    """
    MATLAB scatteredInterpolant类的Python实现
    
    提供散点数据的插值功能，支持多种插值方法
    """
    
    def __init__(self, x: np.ndarray, y: np.ndarray, v: np.ndarray, 
                 method: Literal['linear', 'nearest', 'natural'] = 'linear',
                 extrapolation: Literal['linear', 'nearest', 'none'] = 'linear'):
        """
        初始化散点插值器
        
        参数:
            x, y: 已知数据点坐标
            v: 已知数据点的值
            method: 插值方法
            extrapolation: 外推方法
        """
        self.x = np.asarray(x).ravel()
        self.y = np.asarray(y).ravel()
        self.v = np.asarray(v).ravel()
        self.method = method
        self.extrapolation = extrapolation
        
        if len(self.x) != len(self.y) or len(self.x) != len(self.v):
            raise ValueError("x, y, v必须具有相同的长度")
        
        # 移除无效值
        valid_mask = ~(np.isnan(self.x) | np.isnan(self.y) | np.isnan(self.v))
        self.x = self.x[valid_mask]
        self.y = self.y[valid_mask]
        self.v = self.v[valid_mask]
        
        if len(self.x) < 3:
            raise ValueError("至少需要3个有效数据点")
        
        # 创建插值器
        self.points = np.column_stack((self.x, self.y))
        self._create_interpolator()
    
    def _create_interpolator(self):
        """创建底层插值器"""
        if self.method == 'linear':
            self.interpolator = LinearNDInterpolator(self.points, self.v, 
                                                   fill_value=np.nan)
        elif self.method == 'nearest':
            self.interpolator = NearestNDInterpolator(self.points, self.v)
        elif self.method == 'natural':
            # 使用线性插值作为自然插值的近似
            self.interpolator = LinearNDInterpolator(self.points, self.v, 
                                                   fill_value=np.nan)
        else:
            raise ValueError(f"不支持的插值方法: {self.method}")
    
    def __call__(self, xi: np.ndarray, yi: np.ndarray) -> np.ndarray:
        """
        执行插值
        
        参数:
            xi, yi: 插值目标点坐标
        
        返回:
            插值结果
        """
        xi, yi = np.asarray(xi), np.asarray(yi)
        original_shape = xi.shape
        
        xi_flat = xi.ravel()
        yi_flat = yi.ravel()
        points_i = np.column_stack((xi_flat, yi_flat))
        
        # 执行插值
        result = self.interpolator(points_i)
        
        # 处理外推
        if self.extrapolation == 'none':
            # 将超出凸包的点设为NaN
            pass  # 默认行为
        elif self.extrapolation == 'nearest':
            # 使用最近邻填充NaN值
            nan_mask = np.isnan(result)
            if np.any(nan_mask):
                nearest_interp = NearestNDInterpolator(self.points, self.v)
                result[nan_mask] = nearest_interp(points_i[nan_mask])
        
        return result.reshape(original_shape)

def create_meshgrid(x_range: Tuple[float, float], y_range: Tuple[float, float],
                   x_points: int, y_points: int) -> Tuple[np.ndarray, np.ndarray]:
    """
    创建网格，类似MATLAB的meshgrid
    
    参数:
        x_range: x轴范围 (min, max)
        y_range: y轴范围 (min, max)
        x_points: x方向点数
        y_points: y方向点数
    
    返回:
        X, Y网格坐标
    """
    x = np.linspace(x_range[0], x_range[1], x_points)
    y = np.linspace(y_range[0], y_range[1], y_points)
    return np.meshgrid(x, y, indexing='xy')

def regular_grid_interpolation(points: Tuple[np.ndarray, ...], values: np.ndarray,
                             xi: np.ndarray, method: str = 'linear',
                             bounds_error: bool = False, 
                             fill_value: Optional[float] = None) -> np.ndarray:
    """
    规则网格插值
    
    参数:
        points: 每个维度的坐标数组元组
        values: 网格点的值
        xi: 插值点坐标
        method: 插值方法
        bounds_error: 是否在超出边界时报错
        fill_value: 超出边界时的填充值
    
    返回:
        插值结果
    """
    if fill_value is None:
        fill_value = np.nan
    
    interpolator = RegularGridInterpolator(
        points, values, method=method, 
        bounds_error=bounds_error, fill_value=fill_value
    )
    
    return interpolator(xi)

def adaptive_interpolation(x: np.ndarray, y: np.ndarray, v: np.ndarray,
                         xi: np.ndarray, yi: np.ndarray,
                         density_threshold: float = 0.1) -> np.ndarray:
    """
    自适应插值：根据数据密度选择插值方法
    
    参数:
        x, y: 已知数据点坐标
        v: 已知数据点的值
        xi, yi: 插值目标点坐标
        density_threshold: 密度阈值
    
    返回:
        插值结果
    """
    # 计算数据密度
    x_range = np.ptp(x)
    y_range = np.ptp(y)
    area = x_range * y_range
    density = len(x) / area if area > 0 else 0
    
    # 根据密度选择方法
    if density > density_threshold:
        method = 'cubic'
    elif density > density_threshold / 2:
        method = 'linear'
    else:
        method = 'nearest'
    
    return matlab_griddata(x, y, v, xi, yi, method=method)

def interpolate_missing_values(data: np.ndarray, method: str = 'linear') -> np.ndarray:
    """
    插值填充缺失值
    
    参数:
        data: 包含NaN的二维数组
        method: 插值方法
    
    返回:
        填充后的数组
    """
    result = data.copy()
    nan_mask = np.isnan(data)
    
    if not np.any(nan_mask):
        return result
    
    # 获取有效数据点
    valid_mask = ~nan_mask
    if not np.any(valid_mask):
        warnings.warn("数组中没有有效值")
        return result
    
    # 创建坐标网格
    rows, cols = np.mgrid[0:data.shape[0], 0:data.shape[1]]
    
    # 有效点的坐标和值
    valid_points = np.column_stack((rows[valid_mask], cols[valid_mask]))
    valid_values = data[valid_mask]
    
    # 需要插值的点
    nan_points = np.column_stack((rows[nan_mask], cols[nan_mask]))
    
    # 执行插值
    try:
        interpolated = griddata(valid_points, valid_values, nan_points, 
                              method=method, fill_value=np.nan)
        result[nan_mask] = interpolated
    except Exception as e:
        warnings.warn(f"插值填充失败: {e}")
    
    return result

# 质量评估函数
def interpolation_quality_metrics(x_true: np.ndarray, x_interp: np.ndarray) -> dict:
    """
    评估插值质量
    
    参数:
        x_true: 真实值
        x_interp: 插值结果
    
    返回:
        质量指标字典
    """
    # 移除NaN值
    valid_mask = ~(np.isnan(x_true) | np.isnan(x_interp))
    if not np.any(valid_mask):
        return {'error': '没有有效的比较点'}
    
    true_clean = x_true[valid_mask]
    interp_clean = x_interp[valid_mask]
    
    # 计算各种误差指标
    mae = np.mean(np.abs(true_clean - interp_clean))
    rmse = np.sqrt(np.mean((true_clean - interp_clean)**2))
    
    # 相对误差
    relative_error = np.abs(true_clean - interp_clean) / (np.abs(true_clean) + 1e-10)
    mean_relative_error = np.mean(relative_error)
    
    # 相关系数
    correlation = np.corrcoef(true_clean, interp_clean)[0, 1]
    
    return {
        'mae': mae,
        'rmse': rmse,
        'mean_relative_error': mean_relative_error,
        'correlation': correlation,
        'valid_points': len(true_clean),
        'total_points': len(x_true.ravel())
    } 

class InterpolationEngine:
    """
    插值引擎类
    
    提供各种插值功能，特别是为空间变化的图像补偿设计的插值算法。
    """
    
    def __init__(self):
        """初始化插值引擎"""
        self.interpolators = {}  # 缓存插值器
    
    def create_scattered_interpolator(self, points: np.ndarray, values: np.ndarray,
                                    method: str = 'linear', 
                                    fill_value: Optional[float] = None) -> object:
        """
        创建scattered data插值器 - 对应MATLAB的scatteredInterpolant
        
        参数:
            points: 采样点坐标，形状为(N, 2)，对应MATLAB中的[X, Y]
            values: 采样点的值，形状为(N,)
            method: 插值方法 ('linear', 'nearest', 'cubic')
            fill_value: 外推区域的填充值，None表示使用最近邻外推
            
        返回:
            插值器对象
        """
        # 输入验证
        if not isinstance(points, np.ndarray) or points.ndim != 2 or points.shape[1] != 2:
            raise ValueError("points必须是形状为(N, 2)的2D数组")
        if not isinstance(values, np.ndarray) or values.ndim != 1:
            raise ValueError("values必须是1D数组")
        if len(points) != len(values):
            raise ValueError("points和values的长度必须相同")
        
        # 创建插值器
        if method == 'linear':
            # 使用LinearNDInterpolator，对应MATLAB的'linear'方法
            interpolator = LinearNDInterpolator(points, values, fill_value=fill_value)
        elif method == 'nearest':
            # 使用NearestNDInterpolator，对应MATLAB的'nearest'方法
            interpolator = NearestNDInterpolator(points, values)
        elif method == 'cubic':
            # 对于cubic，使用griddata，因为scipy没有直接的CubicNDInterpolator
            def cubic_interpolator(query_points):
                return griddata(points, values, query_points, method='cubic', 
                              fill_value=fill_value if fill_value is not None else np.nan)
            interpolator = cubic_interpolator
        else:
            raise ValueError(f"不支持的插值方法: {method}")
        
        return interpolator
    
    def spatial_varying_interpolation(self, candidate_images: np.ndarray,
                                    grid_coords: np.ndarray,
                                    method: str = 'linear',
                                    fill_mode: str = 'nearest') -> np.ndarray:
        """
        空间变化插值 - 移植自MATLAB online_apply_compensation.m
        
        对候选图像进行空间插值，生成最终的预补偿图像。
        这是"先卷积，后插值"策略的核心实现。
        
        参数:
            candidate_images: 候选图像数组，形状为(H, W, N)，N为核的数量
            grid_coords: 网格坐标，形状为(N, 2)，对应每个核的位置
            method: 插值方法 ('linear', 'nearest', 'cubic')
            fill_mode: 边界处理模式 ('nearest', 'constant', 'edge')
            
        返回:
            插值后的图像，形状为(H, W)
        """
        # 输入验证
        if not isinstance(candidate_images, np.ndarray) or candidate_images.ndim != 3:
            raise ValueError("candidate_images必须是3D数组 (H, W, N)")
        if not isinstance(grid_coords, np.ndarray) or grid_coords.shape[1] != 2:
            raise ValueError("grid_coords必须是形状为(N, 2)的数组")
        if candidate_images.shape[2] != len(grid_coords):
            raise ValueError("candidate_images的第三维必须与grid_coords的长度相同")
        
        img_h, img_w, num_kernels = candidate_images.shape
        result = np.zeros((img_h, img_w))
        
        # 创建查询点网格 - 对应图像中的每个像素位置
        y_coords, x_coords = np.mgrid[0:img_h, 0:img_w]
        query_points = np.column_stack([x_coords.ravel(), y_coords.ravel()])
        
        # 对每个像素位置进行插值
        for i in range(img_h):
            for j in range(img_w):
                # 当前像素的坐标
                query_point = np.array([[j, i]])  # 注意：[x, y]顺序
                
                # 提取当前像素在所有候选图像中的值
                pixel_values = candidate_images[i, j, :]
                
                # 创建插值器并进行插值
                try:
                    if method == 'linear':
                        interpolator = LinearNDInterpolator(grid_coords, pixel_values, fill_value=np.nan)
                        interpolated_value = interpolator(query_point)[0]
                    elif method == 'nearest':
                        interpolator = NearestNDInterpolator(grid_coords, pixel_values)
                        interpolated_value = interpolator(query_point)[0]
                    elif method == 'cubic':
                        interpolated_value = griddata(grid_coords, pixel_values, query_point, 
                                                    method='cubic', fill_value=np.nan)[0]
                    else:
                        raise ValueError(f"不支持的插值方法: {method}")
                    
                    # 处理NaN值（外推区域）
                    if np.isnan(interpolated_value):
                        if fill_mode == 'nearest':
                            # 使用最近邻填充
                            distances = np.sum((grid_coords - query_point) ** 2, axis=1)
                            nearest_idx = np.argmin(distances)
                            interpolated_value = pixel_values[nearest_idx]
                        elif fill_mode == 'constant':
                            interpolated_value = 0.0
                        elif fill_mode == 'edge':
                            # 使用边界值
                            interpolated_value = np.mean(pixel_values)
                        else:
                            interpolated_value = 0.0
                    
                    result[i, j] = interpolated_value
                    
                except Exception as e:
                    # 处理插值失败的情况
                    warnings.warn(f"插值失败在位置({i}, {j}): {e}")
                    result[i, j] = np.mean(pixel_values)
        
        return result
    
    def optimized_spatial_interpolation(self, candidate_images: np.ndarray,
                                      grid_coords: np.ndarray,
                                      method: str = 'linear') -> np.ndarray:
        """
        优化的空间插值 - 向量化版本，提高性能
        
        参数:
            candidate_images: 候选图像数组，形状为(H, W, N)
            grid_coords: 网格坐标，形状为(N, 2)
            method: 插值方法
            
        返回:
            插值后的图像
        """
        img_h, img_w, num_kernels = candidate_images.shape
        
        # 创建图像坐标网格
        y_coords, x_coords = np.mgrid[0:img_h, 0:img_w]
        
        # 重组为查询点
        query_x = x_coords.ravel()
        query_y = y_coords.ravel()
        
        # 为每个查询点找到合适的插值权重
        if method == 'linear':
            # 使用反距离加权插值的简化版本
            result = np.zeros((img_h, img_w))
            
            for i in range(img_h):
                for j in range(img_w):
                    query_point = np.array([j, i])  # [x, y]
                    
                    # 计算到所有网格点的距离
                    distances = np.linalg.norm(grid_coords - query_point, axis=1)
                    
                    # 避免除零
                    distances = np.maximum(distances, 1e-10)
                    
                    # 计算权重（反距离权重）
                    weights = 1.0 / distances
                    weights = weights / np.sum(weights)
                    
                    # 加权平均
                    pixel_values = candidate_images[i, j, :]
                    result[i, j] = np.sum(weights * pixel_values)
            
            return result
        
        else:
            # 对于其他方法，回退到逐像素插值
            return self.spatial_varying_interpolation(candidate_images, grid_coords, method)
    
    def validate_interpolation_quality(self, original_points: np.ndarray,
                                     original_values: np.ndarray,
                                     interpolated_points: np.ndarray,
                                     interpolated_values: np.ndarray,
                                     tolerance: float = 0.1) -> Tuple[bool, dict]:
        """
        验证插值质量
        
        参数:
            original_points: 原始采样点
            original_values: 原始采样值
            interpolated_points: 插值点
            interpolated_values: 插值结果
            tolerance: 容差
            
        返回:
            (质量是否合格, 质量指标字典)
        """
        metrics = {}
        
        # 计算插值误差（在原始采样点上）
        if len(original_points) > 0:
            # 在原始点上评估插值结果
            tree = cKDTree(interpolated_points)
            distances, indices = tree.query(original_points, k=1)
            
            # 计算最近邻误差
            nearest_values = interpolated_values[indices]
            errors = np.abs(nearest_values - original_values)
            
            metrics['max_error'] = np.max(errors)
            metrics['mean_error'] = np.mean(errors)
            metrics['rmse'] = np.sqrt(np.mean(errors ** 2))
        
        # 检查数值稳定性
        metrics['has_nan'] = np.any(np.isnan(interpolated_values))
        metrics['has_inf'] = np.any(np.isinf(interpolated_values))
        
        # 检查值的合理范围
        value_range = np.max(interpolated_values) - np.min(interpolated_values)
        original_range = np.max(original_values) - np.min(original_values)
        metrics['range_ratio'] = value_range / max(original_range, 1e-10)
        
        # 质量评估
        quality_checks = [
            not metrics['has_nan'],
            not metrics['has_inf'],
            metrics.get('mean_error', 0) < tolerance,
            0.5 < metrics['range_ratio'] < 2.0  # 值域不应该变化太大
        ]
        
        passed = all(quality_checks)
        return passed, metrics


# 便捷函数
def scattered_interpolate(points: np.ndarray, values: np.ndarray, 
                         query_points: np.ndarray, method: str = 'linear',
                         fill_value: Optional[float] = None) -> np.ndarray:
    """
    便捷函数：scattered data插值
    
    与MATLAB的scatteredInterpolant类似的接口。
    
    参数:
        points: 采样点坐标 (N, 2)
        values: 采样点值 (N,)
        query_points: 查询点坐标 (M, 2)
        method: 插值方法
        fill_value: 外推填充值
        
    返回:
        插值结果 (M,)
    """
    engine = InterpolationEngine()
    interpolator = engine.create_scattered_interpolator(points, values, method, fill_value)
    
    if hasattr(interpolator, '__call__'):
        return interpolator(query_points)
    else:
        # 对于cubic方法
        return interpolator(query_points)


def spatial_interpolate_images(candidate_images: np.ndarray, grid_coords: np.ndarray,
                             method: str = 'linear', optimize: bool = True) -> np.ndarray:
    """
    便捷函数：空间变化图像插值
    
    参数:
        candidate_images: 候选图像数组
        grid_coords: 网格坐标
        method: 插值方法
        optimize: 是否使用优化版本
        
    返回:
        插值后的图像
    """
    engine = InterpolationEngine()
    
    if optimize and method == 'linear':
        return engine.optimized_spatial_interpolation(candidate_images, grid_coords, method)
    else:
        return engine.spatial_varying_interpolation(candidate_images, grid_coords, method) 