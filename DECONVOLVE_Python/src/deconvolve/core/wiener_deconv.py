"""
Wiener反卷积算法实现

完全移植自MATLAB deconvolve_wiener函数，提供高精度的图像反卷积处理。
支持频域Wiener滤波，包含数值稳定性保护和能量守恒。

主要功能：
- Wiener反卷积算法
- 批量处理支持
- 自适应噪声比率计算
- 质量验证工具
"""

import numpy as np
from scipy.fft import fft2, ifft2
from typing import Tuple, Optional
import warnings


class WienerDeconvolution:

    def __init__(self):
        """初始化Wiener反卷积处理器"""
        pass

    def wiener_deconvolve(
        self,
        observed: np.ndarray,
        kernel: np.ndarray, 
        noise_ratio: float = 1e-5
    ) -> np.ndarray:
        """
        执行Wiener反卷积
        
        从观测到的模糊信号和已知的模糊核中估计原始信号。
        
        参数:
            observed: 观测到的（模糊的）信号，2D numpy数组
            kernel: 模糊核（PSF或源函数），2D numpy数组
            noise_ratio: 噪声正则化参数，用于噪声抑制
            
        返回:
            反卷积信号估计，2D numpy数组
            
        注意:
            - 输入数组会被自动填充到相同大小
            - 使用FFT进行高效的频域计算
            - 包含数值稳定性保护
        """
        # 输入验证
        if not isinstance(observed, np.ndarray) or observed.ndim != 2:
            raise ValueError("observed必须是2D numpy数组")
        if not isinstance(kernel, np.ndarray) or kernel.ndim != 2:
            raise ValueError("kernel必须是2D numpy数组")
        if noise_ratio <= 0:
            raise ValueError("noise_ratio必须为正数")
        
        # 获取输入尺寸
        obs_h, obs_w = observed.shape
        ker_h, ker_w = kernel.shape
        
        # 确定FFT尺寸（至少为两个输入尺寸之和）
        fft_h = obs_h + ker_h - 1
        fft_w = obs_w + ker_w - 1
        
        # 使尺寸为偶数以获得更好的FFT性能
        fft_h = fft_h + (fft_h % 2)
        fft_w = fft_w + (fft_w % 2)
        
        # 将两个信号填充到相同大小
        observed_padded = np.pad(
            observed, 
            ((0, fft_h - obs_h), (0, fft_w - obs_w)), 
            mode='constant', 
            constant_values=0
        )
        kernel_padded = np.pad(
            kernel,
            ((0, fft_h - ker_h), (0, fft_w - ker_w)),
            mode='constant',
            constant_values=0
        )
        
        # 计算FFT
        G = fft2(kernel_padded)  # 核的频域表示
        S = fft2(observed_padded)  # 观测信号的频域表示
        
        # Wiener反卷积在频域中的实现
        # H = (G* · S) / (|G|² + noise_ratio · |S|²)
        G_conj = np.conj(G)
        G_mag_sq = np.abs(G) ** 2
        S_mag_sq = np.abs(S) ** 2
        
        # 避免除零，添加数值稳定性保护
        denominator = G_mag_sq + noise_ratio * S_mag_sq
        denominator = np.where(denominator < np.finfo(float).eps, np.finfo(float).eps, denominator)
        
        # 检查是否有数值问题
        if np.any(np.isnan(denominator)) or np.any(np.isinf(denominator)):
            warnings.warn("检测到数值不稳定性，可能需要调整noise_ratio参数")
        
        H = (G_conj * S) / denominator
        
        # 转换回空间域
        result_full = np.real(ifft2(H))
        
        # 提取中心部分（与原始观测信号相同大小）
        start_h = (fft_h - obs_h) // 2
        start_w = (fft_w - obs_w) // 2
        result = result_full[start_h:start_h + obs_h, start_w:start_w + obs_w]
        
        # 确保非负值（物理约束）
        result = np.maximum(result, 0)
        
        # 归一化以保持能量
        result_sum = np.sum(result)
        if result_sum > np.finfo(float).eps:
            result = result / result_sum
        else:
            warnings.warn("反卷积结果接近零，使用均匀分布")
            result = np.ones_like(result) / result.size
        
        return result


    def batch_wiener_deconvolve(
        self,
        observed_list: list,
        kernel_list: list,
        noise_ratio: float = 1e-5,
        show_progress: bool = True
    ) -> list:
        """
        批量执行Wiener反卷积
        
        参数:
            observed_list: 观测信号列表
            kernel_list: 核函数列表
            noise_ratio: 噪声正则化参数
            show_progress: 是否显示进度
            
        返回:
            反卷积结果列表
        """
        if len(observed_list) != len(kernel_list):
            raise ValueError("观测信号和核函数列表长度必须相同")
        
        results = []
        total = len(observed_list)
        
        for i, (obs, ker) in enumerate(zip(observed_list, kernel_list)):
            if show_progress and i % 10 == 0:
                # 使用logging而不是print
                import logging
                logger = logging.getLogger(__name__)
                logger.info(f"处理进度: {i+1}/{total} ({(i+1)/total*100:.1f}%)")
                
            result = self.wiener_deconvolve(obs, ker, noise_ratio)
            results.append(result)
        
        if show_progress:
            import logging
            logger = logging.getLogger(__name__)
            logger.info(f"批量处理完成: {total}/{total} (100.0%)")
        
        return results


    def adaptive_noise_ratio(
        self,
        observed: np.ndarray,
        kernel: np.ndarray,
        snr_estimate: Optional[float] = None
    ) -> float:
        """
        自适应计算噪声比率
        
        基于信号特征自动估计合适的noise_ratio参数
        
        参数:
            observed: 观测信号
            kernel: 核函数
            snr_estimate: 可选的信噪比估计
            
        返回:
            推荐的noise_ratio值
        """
        if snr_estimate is not None:
            # 基于SNR的经验公式
            return 1.0 / (snr_estimate ** 2)
        
        # 基于信号统计特征的自动估计
        signal_var = np.var(observed)
        kernel_var = np.var(kernel)
        
        # 经验公式：基于信号和核的方差比
        if kernel_var > 0:
            ratio = signal_var / kernel_var
            noise_ratio = 1.0 / (1.0 + ratio * 100)
        else:
            noise_ratio = 1e-3  # 默认值
        
        # 限制在合理范围内
        noise_ratio = np.clip(noise_ratio, 1e-6, 1e-2)
        
        return noise_ratio


    def validate_deconvolution_quality(
        self,
        original: np.ndarray,
        result: np.ndarray,
        threshold: float = 0.1
    ) -> Tuple[bool, dict]:
        """
        验证反卷积质量
        
        参数:
            original: 原始信号（如果可用）
            result: 反卷积结果
            threshold: 质量阈值
            
        返回:
            (是否通过质量检查, 质量指标字典)
        """
        metrics = {}
        
        # 计算基本统计指标
        metrics['mean'] = np.mean(result)
        metrics['std'] = np.std(result)
        metrics['min'] = np.min(result)
        metrics['max'] = np.max(result)
        
        # 检查数值稳定性
        metrics['has_nan'] = np.any(np.isnan(result))
        metrics['has_inf'] = np.any(np.isinf(result))
        metrics['negative_ratio'] = np.sum(result < 0) / result.size
        
        # 如果有原始信号，计算误差指标
        if original is not None and original.shape == result.shape:
            mse = np.mean((original - result) ** 2)
            metrics['mse'] = mse
            metrics['psnr'] = 10 * np.log10(1.0 / (mse + 1e-10))
            
            # 结构相似性的简单近似
            corr = np.corrcoef(original.flatten(), result.flatten())[0, 1]
            metrics['correlation'] = corr if not np.isnan(corr) else 0.0
        
        # 质量检查
        quality_checks = [
            not metrics['has_nan'],
            not metrics['has_inf'], 
            metrics['negative_ratio'] < threshold,
            metrics['std'] > 1e-10,  # 避免完全平坦的结果
        ]
        
        passed = all(quality_checks)
        
        return passed, metrics 

# 为了向后兼容性提供的快捷函数
def wiener_deconvolve(observed: np.ndarray, kernel: np.ndarray, 
                     noise_ratio: float = 1e-5) -> np.ndarray:
    """
    Wiener反卷积的便捷函数
    
    参数:
        observed: 观测信号（2D numpy数组）
        kernel: 核函数（2D numpy数组）
        noise_ratio: 噪声正则化参数（默认1e-5）
        
    返回:
        反卷积结果
    """
    processor = WienerDeconvolution()
    return processor.wiener_deconvolve(observed, kernel, noise_ratio) 