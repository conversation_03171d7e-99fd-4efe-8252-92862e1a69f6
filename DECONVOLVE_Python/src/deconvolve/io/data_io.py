"""
数据输入输出模块

完全移植自MATLAB helper_parse_speos_data.m的数据解析功能。
支持三种数据格式：
1. 目录中的单独.mat文件（标准格式）
2. 小的文本文件（X, Y, Intensity列）
3. 大型SPEOS导出文件（>100MB，增强处理）
"""

import numpy as np
import scipy.io as sio
import pandas as pd
import pickle
import json
import h5py
from pathlib import Path
from typing import Dict, Any, Union, Optional, List, Tuple
import warnings
import logging
from scipy.interpolate import griddata
from scipy.spatial.distance import cdist


class DataIO:
    """
    数据输入输出类
    
    完全移植自MATLAB版本的数据解析功能
    """
    
    def __init__(self):
        """初始化数据I/O处理器"""
        self.logger = logging.getLogger(__name__)
    
    def parse_speos_data(self, data_source: Union[str, Path], 
                        grid_centers_coords: np.ndarray,
                        matrix_size: int = 31) -> Tuple[List[np.ndarray], np.ndarray]:
        """
        解析BSF数据 - 完全移植自MATLAB helper_parse_speos_data
        
        支持多种输入格式：
        1. 包含单独.mat文件的目录（bsf_1.mat, bsf_2.mat等）
        2. 带有X, Y, Intensity列的单个文本文件（小文件）
        3. 大型SPEOS导出文件，包含数百万数据点（增强处理）
        
        参数:
            data_source: 目录路径或单个文件路径
            grid_centers_coords: 每个BSF的近似中心坐标（36×2矩阵）
            matrix_size: 输出BSF矩阵的大小（通常为31）
            
        返回:
            (bsf_cells, precise_coords): BSF单元数组和精确坐标
        """
        # 输入验证
        data_source = Path(data_source)
        if not data_source.exists():
            raise FileNotFoundError(f"数据源不存在: {data_source}")
        
        if not isinstance(grid_centers_coords, np.ndarray) or grid_centers_coords.shape[1] != 2:
            raise ValueError("grid_centers_coords必须是N×2的数组")
        
        if matrix_size <= 0 or not isinstance(matrix_size, int):
            raise ValueError("matrix_size必须是正整数")
        
        num_bsfs = len(grid_centers_coords)
        self.logger.info(f"解析BSF数据从: {data_source}")
        
        # 确定输入格式并相应解析
        if data_source.is_dir():
            # 格式1：包含单独.mat文件的目录
            self.logger.info("使用目录格式，包含单独的.mat文件")
            bsf_cells, precise_coords = self._parse_mat_files(data_source, num_bsfs, matrix_size)
            
        elif data_source.is_file():
            # 检查是.mat文件还是文本文件
            if data_source.suffix.lower() == '.mat':
                raise ValueError("不支持单个.mat文件格式。请使用包含多个.mat文件的目录或文本文件。")
            else:
                # 格式2：单个文本文件 - 确定是大型SPEOS文件还是小文件
                file_size_mb = data_source.stat().st_size / (1024**2)
                
                if file_size_mb > 100:  # 大文件阈值：100MB
                    self.logger.info(f"检测到大型SPEOS文件({file_size_mb:.1f} MB)。使用增强处理模式")
                    bsf_cells, precise_coords = self._parse_speos_large_file(
                        data_source, grid_centers_coords, matrix_size)
                else:
                    self.logger.info("使用标准文本文件格式，包含X, Y, Intensity列")
                    bsf_cells, precise_coords = self._parse_text_file(
                        data_source, grid_centers_coords, matrix_size)
        else:
            raise ValueError(f"无法确定数据格式: {data_source}")
        
        # 验证结果
        for i, bsf in enumerate(bsf_cells):
            if bsf is None or bsf.shape != (matrix_size, matrix_size):
                raise ValueError(f"BSF {i+1}的尺寸不正确或为空")
            
            if np.any(np.isnan(precise_coords[i])) or np.any(np.isinf(precise_coords[i])):
                self.logger.warning(f"BSF {i+1}的坐标无效。使用原始坐标")
                precise_coords[i] = grid_centers_coords[i]
        
        self.logger.info(f"成功提取了{num_bsfs}个BSF")
        return bsf_cells, precise_coords
    
    def _parse_mat_files(self, data_dir: Path, num_bsfs: int, 
                        matrix_size: int) -> Tuple[List[np.ndarray], np.ndarray]:
        """从单独的.mat文件解析BSF数据 - 移植自MATLAB parse_mat_files"""
        
        bsf_cells = []
        precise_coords = np.zeros((num_bsfs, 2))
        
        for i in range(num_bsfs):
            mat_file = data_dir / f"bsf_{i+1}.mat"
            
            if not mat_file.exists():
                raise FileNotFoundError(f"必需的BSF文件未找到: {mat_file}")
            
            try:
                data = self.load_mat_file(mat_file)
                if 'bsf_data' not in data:
                    raise ValueError(f'文件"{mat_file}"不包含必需的变量"bsf_data"')
                
                bsf_data = data['bsf_data']
                
                # 验证BSF数据
                if not isinstance(bsf_data, np.ndarray) or bsf_data.ndim != 2:
                    raise ValueError(f'"{mat_file}"中的BSF数据必须是2D数值矩阵')
                
                # 如果需要，调整尺寸
                if bsf_data.shape != (matrix_size, matrix_size):
                    self.logger.info(f"调整BSF {i+1}的尺寸从{bsf_data.shape}到({matrix_size}, {matrix_size})")
                    from skimage.transform import resize
                    bsf_data = resize(bsf_data, (matrix_size, matrix_size), 
                                    preserve_range=True, anti_aliasing=True)
                
                # 存储归一化的BSF
                bsf_sum = np.sum(bsf_data)
                if bsf_sum > np.finfo(float).eps:
                    normalized_bsf = bsf_data / bsf_sum
                else:
                    self.logger.warning(f"BSF {i+1}的和为零或接近零。使用均匀分布")
                    normalized_bsf = np.ones((matrix_size, matrix_size)) / (matrix_size**2)
                
                bsf_cells.append(normalized_bsf)
                
                # 计算质心作为精确坐标
                X, Y = np.meshgrid(range(matrix_size), range(matrix_size))
                total_intensity = np.sum(normalized_bsf)
                if total_intensity > np.finfo(float).eps:
                    precise_coords[i, 0] = np.sum(X * normalized_bsf) / total_intensity
                    precise_coords[i, 1] = np.sum(Y * normalized_bsf) / total_intensity
                else:
                    precise_coords[i] = [matrix_size/2, matrix_size/2]
                    
            except Exception as e:
                raise RuntimeError(f'加载BSF文件"{mat_file}"失败: {e}')
        
        return bsf_cells, precise_coords
    
    def _parse_text_file(self, text_file: Path, grid_centers_coords: np.ndarray,
                        matrix_size: int) -> Tuple[List[np.ndarray], np.ndarray]:
        """从文本文件解析BSF数据 - 移植自MATLAB parse_text_file"""
        
        try:
            # 尝试多种读取方法
            for sep in ['\t', ',', ' ', ';']:
                try:
                    raw_data = pd.read_csv(text_file, sep=sep, header=None, 
                                         comment='#', skiprows=0).values
                    if raw_data.shape[1] >= 3:
                        break
                except:
                    continue
            else:
                raise ValueError("无法解析文本文件格式")
                
        except Exception as e:
            raise RuntimeError(f'读取文本文件"{text_file}"失败: {e}')
        
        if raw_data.shape[1] < 3:
            raise ValueError("文本文件必须至少有3列：X, Y, Intensity")
        
        sim_x = raw_data[:, 0]
        sim_y = raw_data[:, 1]
        sim_intensity = raw_data[:, 2]
        
        # 移除无效数据点
        valid_idx = (~np.isnan(sim_x) & ~np.isnan(sim_y) & 
                    ~np.isnan(sim_intensity) & (sim_intensity >= 0))
        sim_x = sim_x[valid_idx]
        sim_y = sim_y[valid_idx]
        sim_intensity = sim_intensity[valid_idx]
        
        if len(sim_x) == 0:
            raise ValueError("文本文件中未找到有效数据点")
        
        num_bsfs = len(grid_centers_coords)
        bsf_cells = []
        precise_coords = np.zeros((num_bsfs, 2))
        half_size = matrix_size // 2
        
        for i in range(num_bsfs):
            center_x, center_y = grid_centers_coords[i]
            
            # 定义围绕近似中心的边界框
            x_min = center_x - half_size
            x_max = center_x + half_size
            y_min = center_y - half_size
            y_max = center_y + half_size
            
            # 找到边界框内的数据点
            idx = ((sim_x >= x_min) & (sim_x <= x_max) & 
                  (sim_y >= y_min) & (sim_y <= y_max))
            
            if np.sum(idx) < 3:
                self.logger.warning(f"BSF {i+1}的数据点不足。使用均匀分布")
                bsf_cells.append(np.ones((matrix_size, matrix_size)) / (matrix_size**2))
                precise_coords[i] = [center_x, center_y]
                continue
            
            local_x = sim_x[idx]
            local_y = sim_y[idx]
            local_intensity = sim_intensity[idx]
            
            # 使用加权质心计算精确中心
            total_intensity = np.sum(local_intensity)
            if total_intensity > np.finfo(float).eps:
                precise_x = np.sum(local_x * local_intensity) / total_intensity
                precise_y = np.sum(local_y * local_intensity) / total_intensity
            else:
                precise_x, precise_y = center_x, center_y
            
            precise_coords[i] = [precise_x, precise_y]
            
            # 创建用于插值的规则网格
            Xq, Yq = np.meshgrid(
                np.linspace(precise_x - half_size, precise_x + half_size, matrix_size),
                np.linspace(precise_y - half_size, precise_y + half_size, matrix_size)
            )
            
            # 将BSF数据插值到规则网格
            try:
                bsf_matrix = griddata(
                    np.column_stack([local_x, local_y]), 
                    local_intensity,
                    (Xq, Yq), 
                    method='linear', 
                    fill_value=0.0
                )
                bsf_matrix = np.nan_to_num(bsf_matrix, nan=0.0)
            except:
                self.logger.warning(f"BSF {i+1}的插值失败。使用均匀分布")
                bsf_matrix = np.ones((matrix_size, matrix_size))
            
            # 归一化BSF
            bsf_sum = np.sum(bsf_matrix)
            if bsf_sum > np.finfo(float).eps:
                bsf_cells.append(bsf_matrix / bsf_sum)
            else:
                bsf_cells.append(np.ones((matrix_size, matrix_size)) / (matrix_size**2))
        
        return bsf_cells, precise_coords
    
    def _parse_speos_large_file(self, speos_file: Path, grid_centers_coords: np.ndarray,
                               matrix_size: int) -> Tuple[List[np.ndarray], np.ndarray]:
        """
        大型SPEOS文件的增强处理 - 移植自MATLAB parse_speos_large_file
        
        处理包含数百万数据点的大型SPEOS仿真文件，使用：
        - 内存高效的算法
        - 智能区域分割
        """
        self.logger.info("初始化增强SPEOS文件处理...")
        
        num_bsfs = len(grid_centers_coords)
        bsf_cells = []
        precise_coords = np.zeros((num_bsfs, 2))
        
        # 处理参数
        chunk_size = 1000000  # 一次处理100万个点
        
        # 初始化数据质量指标
        quality_report = {
            'total_points': 0,
            'valid_points': 0,
            'regions_found': 0,
            'coordinate_precision': np.zeros(num_bsfs),
            'snr_estimates': np.zeros(num_bsfs)
        }
        
        try:
            # 步骤1：分析文件结构和估计数据边界
            self.logger.info("步骤1/5：分析文件结构...")
            file_stats, data_bounds = self._analyze_speos_file(speos_file)
            quality_report['total_points'] = file_stats['total_lines']
            
            # 步骤2：智能区域分割
            self.logger.info("步骤2/5：执行智能区域分割...")
            region_bounds = self._segment_bsf_regions(speos_file, grid_centers_coords, 
                                                     data_bounds, chunk_size)
            quality_report['regions_found'] = len(region_bounds)
            
            # 步骤3：为每个区域提取BSF数据
            self.logger.info(f"步骤3/5：从{num_bsfs}个区域提取BSF数据...")
            for i in range(num_bsfs):
                self.logger.info(f"处理BSF区域{i+1}/{num_bsfs}...")
                
                bsf_data, region_coords, region_quality = self._extract_bsf_region(
                    speos_file, region_bounds[i], matrix_size, chunk_size)
                
                # 存储结果
                bsf_cells.append(bsf_data)
                precise_coords[i] = region_coords
                quality_report['coordinate_precision'][i] = region_quality['coord_precision']
                quality_report['snr_estimates'][i] = region_quality['snr_estimate']
                quality_report['valid_points'] += region_quality['valid_points']
            
            # 步骤4：数据质量验证
            self.logger.info("步骤4/5：执行数据质量验证...")
            self._validate_bsf_quality(bsf_cells, precise_coords, quality_report)
            
            # 步骤5：生成处理报告
            self.logger.info("步骤5/5：生成处理报告...")
            self._generate_processing_report(speos_file, quality_report, 
                                           grid_centers_coords, precise_coords)
            
            self.logger.info("增强SPEOS处理成功完成")
            
        except Exception as e:
            raise RuntimeError(f"增强SPEOS处理失败: {e}")
        
        return bsf_cells, precise_coords
    
    def _analyze_speos_file(self, filename: Path) -> Tuple[Dict, Dict]:
        """分析SPEOS文件结构和估计数据边界"""
        
        self.logger.info("分析文件结构和数据边界...")
        
        file_stats = {
            'file_size': filename.stat().st_size,
            'total_lines': 0,
            'delimiter': None
        }
        
        # 读取样本数据以确定格式和边界
        sample_size = min(10000, file_stats['file_size'] // 100)
        
        try:
            # 读取前几行以检测分隔符和格式
            with open(filename, 'r') as f:
                sample_lines = []
                for _ in range(100):
                    line = f.readline()
                    if not line:
                        break
                    line = line.strip()
                    if line and not line.startswith('#') and not line.startswith('%'):
                        sample_lines.append(line)
            
            # 检测分隔符
            delimiters = [',', '\t', ' ', ';']
            delimiter_counts = [line.count(delim) for delim in delimiters for line in sample_lines[:1]]
            best_delim_idx = np.argmax([sample_lines[0].count(delim) for delim in delimiters])
            file_stats['delimiter'] = delimiters[best_delim_idx]
            
            # 解析样本数据以估计边界
            sample_data = []
            for line in sample_lines[:1000]:
                try:
                    if file_stats['delimiter'] == ' ':
                        values = [float(x) for x in line.split() if x]
                    else:
                        values = [float(x) for x in line.split(file_stats['delimiter'])]
                    
                    if len(values) >= 3 and all(not np.isnan(v) for v in values[:3]):
                        sample_data.append(values[:3])
                except:
                    continue
            
            if not sample_data:
                raise ValueError("样本中未找到有效数据。检查文件格式")
            
            sample_data = np.array(sample_data)
            
            # 估计数据边界
            data_bounds = {
                'x_min': np.min(sample_data[:, 0]),
                'x_max': np.max(sample_data[:, 0]),
                'y_min': np.min(sample_data[:, 1]),
                'y_max': np.max(sample_data[:, 1]),
                'intensity_min': np.min(sample_data[:, 2]),
                'intensity_max': np.max(sample_data[:, 2])
            }
            
            # 估计总行数（粗略近似）
            avg_line_length = np.mean([len(line) for line in sample_lines])
            file_stats['total_lines'] = int(file_stats['file_size'] / avg_line_length)
            
            self.logger.info(f"文件分析完成：~{file_stats['total_lines']}个数据点，"
                           f"边界X[{data_bounds['x_min']:.1f}, {data_bounds['x_max']:.1f}] "
                           f"Y[{data_bounds['y_min']:.1f}, {data_bounds['y_max']:.1f}]")
            
        except Exception as e:
            raise RuntimeError(f"文件分析失败: {e}")
        
        return file_stats, data_bounds
    
    def _segment_bsf_regions(self, speos_file: Path, grid_centers_coords: np.ndarray,
                            data_bounds: Dict, chunk_size: int) -> List[Dict]:
        """智能BSF区域分割"""
        
        num_bsfs = len(grid_centers_coords)
        region_bounds = []
        
        # 基于网格坐标估计每个BSF的边界
        for i in range(num_bsfs):
            center_x, center_y = grid_centers_coords[i]
            
            # 估计区域大小（基于相邻点的距离）
            if len(grid_centers_coords) > 1:
                distances = cdist([grid_centers_coords[i]], grid_centers_coords)[0]
                distances = distances[distances > 0]  # 排除自身
                region_size = np.min(distances) / 2 if len(distances) > 0 else 50
            else:
                region_size = 50  # 默认区域大小
            
            bounds = {
                'x_min': center_x - region_size,
                'x_max': center_x + region_size,
                'y_min': center_y - region_size,
                'y_max': center_y + region_size,
                'center': [center_x, center_y]
            }
            region_bounds.append(bounds)
        
        return region_bounds
    
    def _extract_bsf_region(self, speos_file: Path, region_bound: Dict,
                           matrix_size: int, chunk_size: int) -> Tuple[np.ndarray, np.ndarray, Dict]:
        """从指定区域提取BSF数据"""
        
        # 收集区域内的数据点
        region_data = []
        
        try:
            with open(speos_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if not line or line.startswith('#') or line.startswith('%'):
                        continue
                    
                    try:
                        values = [float(x) for x in line.split()]
                        if len(values) >= 3:
                            x, y, intensity = values[:3]
                            
                            # 检查是否在区域边界内
                            if (region_bound['x_min'] <= x <= region_bound['x_max'] and
                                region_bound['y_min'] <= y <= region_bound['y_max'] and
                                intensity >= 0):
                                region_data.append([x, y, intensity])
                    except:
                        continue
        
        except Exception as e:
            self.logger.warning(f"读取区域数据时出错: {e}")
        
        if len(region_data) < 3:
            self.logger.warning("区域数据点不足，使用均匀分布")
            bsf_data = np.ones((matrix_size, matrix_size)) / (matrix_size**2)
            region_coords = np.array(region_bound['center'])
            quality = {
                'coord_precision': 0.0,
                'snr_estimate': 1.0,
                'valid_points': 0
            }
            return bsf_data, region_coords, quality
        
        region_data = np.array(region_data)
        x_data = region_data[:, 0]
        y_data = region_data[:, 1]
        intensity_data = region_data[:, 2]
        
        # 计算精确的质心坐标
        total_intensity = np.sum(intensity_data)
        if total_intensity > 0:
            precise_x = np.sum(x_data * intensity_data) / total_intensity
            precise_y = np.sum(y_data * intensity_data) / total_intensity
        else:
            precise_x, precise_y = region_bound['center']
        
        region_coords = np.array([precise_x, precise_y])
        
        # 创建规则网格并插值
        half_size = matrix_size // 2
        Xq, Yq = np.meshgrid(
            np.linspace(precise_x - half_size, precise_x + half_size, matrix_size),
            np.linspace(precise_y - half_size, precise_y + half_size, matrix_size)
        )
        
        try:
            bsf_matrix = griddata(
                np.column_stack([x_data, y_data]),
                intensity_data,
                (Xq, Yq),
                method='linear',
                fill_value=0.0
            )
            bsf_matrix = np.nan_to_num(bsf_matrix, nan=0.0)
        except:
            bsf_matrix = np.ones((matrix_size, matrix_size))
        
        # 归一化
        bsf_sum = np.sum(bsf_matrix)
        if bsf_sum > 0:
            bsf_data = bsf_matrix / bsf_sum
        else:
            bsf_data = np.ones((matrix_size, matrix_size)) / (matrix_size**2)
        
        # 计算质量指标
        coord_precision = np.std([precise_x - region_bound['center'][0],
                                 precise_y - region_bound['center'][1]])
        snr_estimate = np.max(intensity_data) / (np.mean(intensity_data) + np.finfo(float).eps)
        
        quality = {
            'coord_precision': coord_precision,
            'snr_estimate': snr_estimate,
            'valid_points': len(region_data)
        }
        
        return bsf_data, region_coords, quality
    
    def _validate_bsf_quality(self, bsf_cells: List[np.ndarray], 
                             precise_coords: np.ndarray, quality_report: Dict) -> None:
        """验证BSF数据质量"""
        
        for i, bsf in enumerate(bsf_cells):
            # 检查数值稳定性
            if np.any(np.isnan(bsf)) or np.any(np.isinf(bsf)):
                self.logger.warning(f"BSF {i+1}包含NaN或Inf值")
            
            # 检查归一化
            bsf_sum = np.sum(bsf)
            if abs(bsf_sum - 1.0) > 1e-6:
                self.logger.warning(f"BSF {i+1}归一化不正确，和={bsf_sum:.6f}")
    
    def _generate_processing_report(self, speos_file: Path, quality_report: Dict,
                                  original_coords: np.ndarray, precise_coords: np.ndarray) -> None:
        """生成处理报告"""
        
        self.logger.info("=== SPEOS处理报告 ===")
        self.logger.info(f"文件: {speos_file}")
        self.logger.info(f"总数据点: {quality_report['total_points']}")
        self.logger.info(f"有效数据点: {quality_report['valid_points']}")
        self.logger.info(f"发现区域: {quality_report['regions_found']}")
        
        # 坐标精度统计
        coord_errors = np.linalg.norm(precise_coords - original_coords, axis=1)
        self.logger.info(f"坐标精度 - 平均误差: {np.mean(coord_errors):.3f}")
        self.logger.info(f"坐标精度 - 最大误差: {np.max(coord_errors):.3f}")
        
        # SNR统计
        avg_snr = np.mean(quality_report['snr_estimates'])
        self.logger.info(f"平均信噪比: {avg_snr:.2f}")
    
    def load_mat_file(self, filepath: Union[str, Path], 
                     variable_names: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        加载MATLAB .mat文件
        
        参数:
            filepath: 文件路径
            variable_names: 要加载的变量名列表，None表示加载全部
        
        返回:
            包含变量的字典
        """
        filepath = Path(filepath)
        if not filepath.exists():
            raise FileNotFoundError(f"文件不存在: {filepath}")
        
        try:
            # 首先尝试使用scipy.io.loadmat
            data = sio.loadmat(str(filepath), variable_names=variable_names)
            
            # 移除MATLAB特有的元数据
            clean_data = {}
            for key, value in data.items():
                if not key.startswith('__'):
                    clean_data[key] = value
            
            return clean_data
            
        except Exception as e:
            # 如果失败，尝试使用h5py（适用于v7.3格式）
            try:
                with h5py.File(str(filepath), 'r') as f:
                    clean_data = {}
                    if variable_names is None:
                        variable_names = list(f.keys())
                    
                    for var_name in variable_names:
                        if var_name in f:
                            clean_data[var_name] = np.array(f[var_name])
                    
                    return clean_data
                    
            except Exception as e2:
                raise RuntimeError(f"无法加载MAT文件: {e}, {e2}")


# 便捷函数
def load_mat_file(filepath: Union[str, Path], 
                 variable_names: Optional[List[str]] = None) -> Dict[str, Any]:
    """便捷函数：加载MATLAB .mat文件"""
    io = DataIO()
    return io.load_mat_file(filepath, variable_names)


def parse_bsf_data(data_source: Union[str, Path], 
                  grid_centers_coords: np.ndarray,
                  matrix_size: int = 31) -> Tuple[List[np.ndarray], np.ndarray]:
    """便捷函数：解析BSF数据"""
    io = DataIO()
    return io.parse_speos_data(data_source, grid_centers_coords, matrix_size)

def save_mat_file(filepath: Union[str, Path], data: Dict[str, Any], 
                 format: str = 'v7') -> None:
    """
    保存数据到MATLAB .mat文件
    
    参数:
        filepath: 文件路径
        data: 要保存的数据字典
        format: 文件格式 ('v7', 'v7.3')
    """
    filepath = Path(filepath)
    filepath.parent.mkdir(parents=True, exist_ok=True)
    
    try:
        if format == 'v7.3':
            # 使用h5py保存为v7.3格式
            with h5py.File(str(filepath), 'w') as f:
                for key, value in data.items():
                    f.create_dataset(key, data=value)
        else:
            # 使用scipy.io保存为v7格式
            sio.savemat(str(filepath), data, format='5')
            
    except Exception as e:
        raise RuntimeError(f"保存MAT文件失败: {e}")

def load_bsf_data(data_dir: Union[str, Path], 
                 file_pattern: str = "bsf_*.mat") -> Dict[int, np.ndarray]:
    """
    批量加载BSF数据文件
    
    参数:
        data_dir: 数据目录
        file_pattern: 文件名模式
    
    返回:
        索引到BSF数据的映射字典
    """
    data_dir = Path(data_dir)
    if not data_dir.exists():
        raise FileNotFoundError(f"数据目录不存在: {data_dir}")
    
    bsf_data = {}
    pattern = file_pattern.replace('*', '[0-9]*')
    
    # 查找所有匹配的文件
    mat_files = list(data_dir.glob(file_pattern))
    mat_files.sort()  # 确保顺序
    
    for mat_file in mat_files:
        try:
            # 从文件名提取索引
            stem = mat_file.stem
            if 'bsf_' in stem:
                index_str = stem.split('bsf_')[1]
                index = int(index_str)
                
                # 加载数据
                data = load_mat_file(mat_file)
                
                # 查找BSF变量（可能的变量名）
                bsf_var = None
                for key in ['bsf', 'BSF', 'data', 'Data']:
                    if key in data:
                        bsf_var = data[key]
                        break
                
                if bsf_var is None:
                    # 如果没找到特定变量，取第一个数值数组
                    for key, value in data.items():
                        if isinstance(value, np.ndarray):
                            bsf_var = value
                            break
                
                if bsf_var is not None:
                    bsf_data[index] = bsf_var
                else:
                    warnings.warn(f"在文件 {mat_file} 中未找到BSF数据")
                    
        except Exception as e:
            warnings.warn(f"加载文件 {mat_file} 失败: {e}")
    
    return bsf_data

def save_python_data(filepath: Union[str, Path], data: Any, 
                    format: str = 'pickle') -> None:
    """
    保存Python数据
    
    参数:
        filepath: 文件路径
        data: 要保存的数据
        format: 保存格式 ('pickle', 'json', 'npz')
    """
    filepath = Path(filepath)
    filepath.parent.mkdir(parents=True, exist_ok=True)
    
    if format == 'pickle':
        with open(filepath, 'wb') as f:
            pickle.dump(data, f, protocol=pickle.HIGHEST_PROTOCOL)
    elif format == 'json':
        # 转换numpy数组为列表
        def convert_numpy(obj):
            if isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, dict):
                return {k: convert_numpy(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy(item) for item in obj]
            return obj
        
        json_data = convert_numpy(data)
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, indent=2, ensure_ascii=False)
    elif format == 'npz':
        if isinstance(data, dict):
            np.savez_compressed(filepath, **data)
        else:
            np.savez_compressed(filepath, data=data)
    else:
        raise ValueError(f"不支持的格式: {format}")

def load_python_data(filepath: Union[str, Path], 
                    format: Optional[str] = None) -> Any:
    """
    加载Python数据
    
    参数:
        filepath: 文件路径
        format: 数据格式，None表示自动检测
    
    返回:
        加载的数据
    """
    filepath = Path(filepath)
    if not filepath.exists():
        raise FileNotFoundError(f"文件不存在: {filepath}")
    
    # 自动检测格式
    if format is None:
        suffix = filepath.suffix.lower()
        if suffix == '.pkl' or suffix == '.pickle':
            format = 'pickle'
        elif suffix == '.json':
            format = 'json'
        elif suffix == '.npz':
            format = 'npz'
        else:
            raise ValueError(f"无法识别文件格式: {suffix}")
    
    if format == 'pickle':
        with open(filepath, 'rb') as f:
            return pickle.load(f)
    elif format == 'json':
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    elif format == 'npz':
        data = np.load(filepath)
        if len(data.files) == 1 and 'data' in data.files:
            return data['data']
        else:
            return {key: data[key] for key in data.files}
    else:
        raise ValueError(f"不支持的格式: {format}")

def create_data_structure(bsf_data: Dict[int, np.ndarray], 
                         metadata: Optional[Dict] = None) -> Dict[str, Any]:
    """
    创建标准化的数据结构
    
    参数:
        bsf_data: BSF数据字典
        metadata: 元数据
    
    返回:
        标准化数据结构
    """
    if metadata is None:
        metadata = {}
    
    # 计算基本统计信息
    total_files = len(bsf_data)
    if total_files > 0:
        sample_shape = next(iter(bsf_data.values())).shape
        data_type = next(iter(bsf_data.values())).dtype
    else:
        sample_shape = None
        data_type = None
    
    structure = {
        'bsf_data': bsf_data,
        'metadata': {
            'total_files': total_files,
            'sample_shape': sample_shape,
            'data_type': str(data_type) if data_type else None,
            'indices': sorted(bsf_data.keys()) if bsf_data else [],
            **metadata
        },
        'version': '1.0',
        'created_by': 'deconvolve_python'
    }
    
    return structure

def validate_data_integrity(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    验证数据完整性
    
    参数:
        data: 数据结构
    
    返回:
        验证结果
    """
    results = {
        'valid': True,
        'errors': [],
        'warnings': [],
        'statistics': {}
    }
    
    # 检查必要字段
    required_fields = ['bsf_data', 'metadata']
    for field in required_fields:
        if field not in data:
            results['errors'].append(f"缺少必要字段: {field}")
            results['valid'] = False
    
    if 'bsf_data' in data:
        bsf_data = data['bsf_data']
        
        # 检查数据类型
        if not isinstance(bsf_data, dict):
            results['errors'].append("bsf_data必须是字典类型")
            results['valid'] = False
        else:
            # 检查数据一致性
            shapes = []
            dtypes = []
            
            for idx, arr in bsf_data.items():
                if not isinstance(arr, np.ndarray):
                    results['warnings'].append(f"索引 {idx} 的数据不是numpy数组")
                else:
                    shapes.append(arr.shape)
                    dtypes.append(arr.dtype)
            
            # 统计信息
            results['statistics'] = {
                'total_arrays': len(bsf_data),
                'unique_shapes': len(set(shapes)),
                'unique_dtypes': len(set(str(dt) for dt in dtypes)),
                'total_memory_mb': sum(arr.nbytes for arr in bsf_data.values() 
                                     if isinstance(arr, np.ndarray)) / (1024*1024)
            }
            
            # 检查形状一致性
            if len(set(shapes)) > 1:
                results['warnings'].append(f"数据形状不一致: {set(shapes)}")
            
            # 检查数据类型一致性
            if len(set(str(dt) for dt in dtypes)) > 1:
                results['warnings'].append(f"数据类型不一致: {set(str(dt) for dt in dtypes)}")
    
    return results

def export_to_matlab_format(data: Dict[str, Any], 
                          output_dir: Union[str, Path]) -> None:
    """
    将数据导出为MATLAB格式
    
    参数:
        data: 数据结构
        output_dir: 输出目录
    """
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    if 'bsf_data' not in data:
        raise ValueError("数据结构中缺少bsf_data")
    
    bsf_data = data['bsf_data']
    
    # 保存每个BSF文件
    for idx, arr in bsf_data.items():
        filename = f"bsf_{idx}.mat"
        filepath = output_dir / filename
        save_mat_file(filepath, {'bsf': arr})
    
    # 保存元数据
    if 'metadata' in data:
        metadata_file = output_dir / 'metadata.mat'
        save_mat_file(metadata_file, {'metadata': data['metadata']})

# 文件操作工具函数
def get_file_info(filepath: Union[str, Path]) -> Dict[str, Any]:
    """
    获取文件信息
    
    参数:
        filepath: 文件路径
    
    返回:
        文件信息字典
    """
    filepath = Path(filepath)
    
    if not filepath.exists():
        return {'exists': False}
    
    stat = filepath.stat()
    
    return {
        'exists': True,
        'size_bytes': stat.st_size,
        'size_mb': stat.st_size / (1024 * 1024),
        'modified_time': stat.st_mtime,
        'suffix': filepath.suffix,
        'stem': filepath.stem,
        'name': filepath.name
    } 