"""
验证工具模块

提供数据质量验证、系统测试和结果验证功能。
用于确保处理流程的正确性和数据完整性。

主要功能：
- BSF数据质量验证
- 核地图验证
- 图像处理结果验证
- 系统测试功能
"""

import numpy as np
from typing import Dict, Tuple, Union, Any
import logging
from pathlib import Path
import warnings


class ValidationUtils:
    """
    验证工具类
    
    提供各种验证和质量检查功能
    """
    
    def __init__(self):
        """初始化验证工具"""
        self.logger = logging.getLogger(__name__)
    
    def validate_bsf_data(self, bsf_data: np.ndarray, 
                         tolerance: float = 1e-6) -> Dict[str, Any]:
        """
        验证BSF数据质量
        
        参数:
            bsf_data: BSF数据矩阵
            tolerance: 数值容差
            
        返回:
            验证结果字典
        """
        results = {
            'is_valid': True,
            'issues': [],
            'statistics': {}
        }
        
        try:
            # 基本格式检查
            if not isinstance(bsf_data, np.ndarray):
                results['is_valid'] = False
                results['issues'].append("BSF数据必须是numpy数组")
                return results
            
            if bsf_data.ndim != 2:
                results['is_valid'] = False
                results['issues'].append(f"BSF数据必须是2D矩阵，当前维度: {bsf_data.ndim}")
                return results
            
            # 数值有效性检查
            if np.any(np.isnan(bsf_data)):
                results['is_valid'] = False
                results['issues'].append("BSF数据包含NaN值")
            
            if np.any(np.isinf(bsf_data)):
                results['is_valid'] = False
                results['issues'].append("BSF数据包含Inf值")
            
            if np.any(bsf_data < 0):
                results['issues'].append("BSF数据包含负值")
            
            # 归一化检查
            data_sum = np.sum(bsf_data)
            if abs(data_sum - 1.0) > tolerance:
                results['issues'].append(f"BSF数据未正确归一化，和={data_sum:.6f}")
            
            # 统计信息
            results['statistics'] = {
                'shape': bsf_data.shape,
                'sum': float(data_sum),
                'min': float(np.min(bsf_data)),
                'max': float(np.max(bsf_data)),
                'mean': float(np.mean(bsf_data)),
                'std': float(np.std(bsf_data)),
                'energy': float(np.sum(bsf_data**2))
            }
            
        except Exception as e:
            results['is_valid'] = False
            results['issues'].append(f"验证过程出错: {e}")
        
        return results
    
    def validate_kernel_map(self, kernel_map: Any, 
                           grid_dims: Tuple[int, int]) -> Dict[str, Any]:
        """
        验证核地图数据
        
        参数:
            kernel_map: 核地图数据
            grid_dims: 网格维度
            
        返回:
            验证结果字典
        """
        results = {
            'is_valid': True,
            'issues': [],
            'statistics': {}
        }
        
        try:
            expected_kernels = np.prod(grid_dims)
            
            if isinstance(kernel_map, list):
                # 嵌套列表格式
                if len(kernel_map) != grid_dims[0]:
                    results['is_valid'] = False
                    results['issues'].append(f"核地图行数不匹配：期望{grid_dims[0]}，实际{len(kernel_map)}")
                
                actual_kernels = 0
                for i, row in enumerate(kernel_map):
                    if isinstance(row, list):
                        if len(row) != grid_dims[1]:
                            results['issues'].append(f"第{i}行核数量不匹配：期望{grid_dims[1]}，实际{len(row)}")
                        actual_kernels += len(row)
                    else:
                        actual_kernels += 1
                
                if actual_kernels != expected_kernels:
                    results['issues'].append(f"核总数不匹配：期望{expected_kernels}，实际{actual_kernels}")
            
            elif isinstance(kernel_map, dict):
                if len(kernel_map) != expected_kernels:
                    results['issues'].append(f"核数量不匹配：期望{expected_kernels}，实际{len(kernel_map)}")
            
            results['statistics']['expected_kernels'] = expected_kernels
            
        except Exception as e:
            results['is_valid'] = False
            results['issues'].append(f"验证过程出错: {e}")
        
        return results
    
    def validate_image_processing(self, original: np.ndarray, 
                                 processed: np.ndarray) -> Dict[str, Any]:
        """
        验证图像处理结果
        
        参数:
            original: 原始图像
            processed: 处理后图像
            
        返回:
            验证结果字典
        """
        results = {
            'is_valid': True,
            'issues': [],
            'metrics': {}
        }
        
        try:
            # 基本检查
            if original.shape != processed.shape:
                results['is_valid'] = False
                results['issues'].append(f"图像尺寸不匹配：原始{original.shape}，处理后{processed.shape}")
                return results
            
            # 数值有效性
            if np.any(np.isnan(processed)):
                results['is_valid'] = False
                results['issues'].append("处理后图像包含NaN值")
            
            if np.any(np.isinf(processed)):
                results['is_valid'] = False
                results['issues'].append("处理后图像包含Inf值")
            
            # 计算质量指标
            mse = np.mean((original - processed)**2)
            psnr = 20 * np.log10(1.0 / np.sqrt(mse)) if mse > 0 else float('inf')
            
            results['metrics'] = {
                'mse': float(mse),
                'psnr': float(psnr),
                'original_mean': float(np.mean(original)),
                'processed_mean': float(np.mean(processed)),
                'original_std': float(np.std(original)),
                'processed_std': float(np.std(processed))
            }
            
        except Exception as e:
            results['is_valid'] = False
            results['issues'].append(f"验证过程出错: {e}")
        
        return results
    
    def run_system_test(self, test_data_dir: Union[str, Path]) -> Dict[str, Any]:
        """
        运行系统测试
        
        参数:
            test_data_dir: 测试数据目录
            
        返回:
            测试结果字典
        """
        results = {
            'overall_pass': True,
            'tests': {},
            'summary': {}
        }
        
        test_data_dir = Path(test_data_dir)
        
        try:
            # 测试1：BSF数据加载
            self.logger.info("运行BSF数据加载测试...")
            bsf_test = self._test_bsf_loading(test_data_dir)
            results['tests']['bsf_loading'] = bsf_test
            if not bsf_test['passed']:
                results['overall_pass'] = False
            
            # 测试2：核地图生成（如果有仿真数据）
            simulation_file = test_data_dir / "simulation_data.txt"
            if simulation_file.exists():
                self.logger.info("运行核地图生成测试...")
                kernel_test = self._test_kernel_generation(simulation_file)
                results['tests']['kernel_generation'] = kernel_test
                if not kernel_test['passed']:
                    results['overall_pass'] = False
            
            # 测试3：图像处理
            test_image = self._create_test_image()
            self.logger.info("运行图像处理测试...")
            image_test = self._test_image_processing(test_image)
            results['tests']['image_processing'] = image_test
            if not image_test['passed']:
                results['overall_pass'] = False
            
            # 生成摘要
            results['summary'] = self._generate_test_summary(results['tests'])
            
        except Exception as e:
            results['overall_pass'] = False
            results['error'] = str(e)
            self.logger.error(f"系统测试失败: {e}")
        
        return results
    
    def _test_bsf_loading(self, data_dir: Path) -> Dict[str, Any]:
        """测试BSF数据加载"""
        test_result = {'passed': True, 'issues': []}
        
        try:
            from ..io.data_io import DataIO
            data_io = DataIO()
            
            # 查找BSF文件
            bsf_files = list(data_dir.glob("bsf_*.mat"))
            if not bsf_files:
                test_result['passed'] = False
                test_result['issues'].append("未找到BSF数据文件")
                return test_result
            
            # 尝试加载第一个文件
            test_file = bsf_files[0]
            data = data_io.load_mat_file(test_file)
            
            if not data:
                test_result['passed'] = False
                test_result['issues'].append("BSF文件加载为空")
            
            test_result['loaded_files'] = len(bsf_files)
            
        except Exception as e:
            test_result['passed'] = False
            test_result['issues'].append(f"BSF加载测试失败: {e}")
        
        return test_result
    
    def _test_kernel_generation(self, simulation_file: Path) -> Dict[str, Any]:
        """测试核地图生成"""
        test_result = {'passed': True, 'issues': []}
        
        try:
            from ..workflow.offline_stage import OfflineStage
            
            offline = OfflineStage()
            
            # 创建测试参数
            grid_coords = np.random.rand(36, 2) * 100
            
            # 这里只是验证离线阶段对象能正确初始化
            # 实际的核地图生成需要真实的仿真数据
            test_result['initialized'] = True
            
        except Exception as e:
            test_result['passed'] = False
            test_result['issues'].append(f"核地图生成测试失败: {e}")
        
        return test_result
    
    def _test_image_processing(self, test_image: np.ndarray) -> Dict[str, Any]:
        """测试图像处理"""
        test_result = {'passed': True, 'issues': []}
        
        try:
            from ..core.image_processing import ImageProcessor
            
            processor = ImageProcessor()
            
            # 测试下采样
            downsampled = processor.downsample_kernel_energy_preserving(test_image, 2)
            
            if downsampled.shape != (test_image.shape[0]//2, test_image.shape[1]//2):
                test_result['passed'] = False
                test_result['issues'].append("下采样尺寸不正确")
            
            # 能量守恒检查
            original_energy = np.sum(test_image)
            downsampled_energy = np.sum(downsampled)
            energy_ratio = downsampled_energy / original_energy
            
            if abs(energy_ratio - 1.0) > 0.1:  # 10%容差
                test_result['issues'].append(f"能量不守恒，比率: {energy_ratio:.3f}")
            
        except Exception as e:
            test_result['passed'] = False
            test_result['issues'].append(f"图像处理测试失败: {e}")
        
        return test_result
    
    def _create_test_image(self, size: int = 64) -> np.ndarray:
        """创建测试图像"""
        # 创建一个简单的高斯分布测试图像
        x, y = np.meshgrid(np.arange(size), np.arange(size))
        center = size // 2
        sigma = size // 6
        
        test_image = np.exp(-((x - center)**2 + (y - center)**2) / (2 * sigma**2))
        test_image = test_image / np.sum(test_image)  # 归一化
        
        return test_image
    
    def _generate_test_summary(self, tests: Dict[str, Any]) -> Dict[str, Any]:
        """生成测试摘要"""
        total_tests = len(tests)
        passed_tests = sum(1 for test in tests.values() if test.get('passed', False))
        
        summary = {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': total_tests - passed_tests,
            'success_rate': passed_tests / total_tests if total_tests > 0 else 0.0
        }
        
        return summary 