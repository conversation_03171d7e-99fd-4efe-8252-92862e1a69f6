"""
工作流模块 - 完整的预补偿处理流程

该模块包含：
- 主工作流程：从原始UI图像到预补偿图像的完整处理
- 离线预计算：BSF核预处理和缓存
- 在线补偿：实时图像预补偿处理
"""

from .main_workflow import MainWorkflow
from .offline_stage import OfflineStage
from .online_stage import OnlineStage

# 导入便捷函数
from .offline_stage import generate_prekernel_map
from .online_stage import apply_compensation, load_and_apply_compensation

__all__ = [
    'MainWorkflow',
    'OfflineStage', 
    'OnlineStage',
    'generate_prekernel_map',
    'apply_compensation',
    'load_and_apply_compensation'
] 