"""
主工作流模块

集成离线和在线两个处理阶段，提供完整的HUD光学预补偿处理流程。
从MATLAB main_workflow.m完全移植。

主要功能：
- 集成离线预计算和在线补偿
- 端到端的图像处理流程
- 配置管理和缓存支持
- 批量处理和结果管理
"""

import numpy as np
import logging
import time
from pathlib import Path
from typing import Dict, List, Optional, Union, Any

from ..workflow.offline_stage import OfflineStage
from ..workflow.online_stage import OnlineStage
from ..io.data_io import DataIO
from ..utils.validation import ValidationUtils


class MainWorkflow:
    """
    主工作流程类
    
    实现完整的HUD光学预补偿处理流程：
    1. 离线阶段：从仿真数据生成预补偿核地图
    2. 在线阶段：对输入图像进行实时预补偿
    
    完全移植自MATLAB main_workflow.m的功能
    """
    
    def __init__(self, config: Optional[Dict] = None, cache_dir: Optional[Union[str, Path]] = None):
        """
        初始化主工作流程
        
        参数:
            config: 配置参数字典
            cache_dir: 缓存目录路径（可选）
        """
        self.cache_dir = Path(cache_dir) if cache_dir else None
        
        # 默认配置参数（与MATLAB版本一致）
        self.config = {
            # 网格参数
            'grid_dims': (6, 6),  # 6×6网格，36个BSF
            'matrix_size': 31,     # BSF提取矩阵大小
            'oversampling_rate': 4,  # 仿真与UI图像的分辨率比率
            
            # 算法参数
            'noise_r1': 1e-5,      # BSF→PSF Wiener解卷积噪声比率
            'noise_r2': 1e-3,      # PSF→PreKernel Wiener解卷积噪声比率
            'interpolation_method': 'linear',  # 空间插值方法
            
            # 处理参数
            'enable_caching': True,
            'quality_threshold': 0.8,
            'max_iterations': 10,
            'parallel_processing': False,
            
            # 输出参数
            'save_intermediate': False,
            'output_format': 'png'
        }
        
        if config:
            self.config.update(config)
        
        # 初始化组件
        self.offline_stage = OfflineStage()
        self.online_stage = OnlineStage()
        self.data_io = DataIO()
        
        # 内部状态
        self.prekernel_map_file = None
        self.is_offline_complete = False
        self.is_online_ready = False
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
        logging.basicConfig(level=logging.INFO)
    
    def run_offline_stage(self, 
                         simulation_file: Union[str, Path],
                         grid_centers_coords: np.ndarray,
                         output_map_file: Union[str, Path]) -> bool:
        """
        运行离线阶段 - 完全移植自MATLAB offline_stage_auto
        
        从仿真数据生成预补偿核地图
        
        参数:
            simulation_file: 仿真数据文件路径
            grid_centers_coords: 网格中心的高分辨率坐标（36×2）
            output_map_file: 输出核地图文件路径
            
        返回:
            离线阶段是否成功完成
        """
        try:
            self.logger.info("=== 开始离线阶段处理 ===")
            start_time = time.time()
            
            # 验证输入参数
            if not Path(simulation_file).exists():
                raise FileNotFoundError(f"仿真文件不存在: {simulation_file}")
            
            if grid_centers_coords.shape != (np.prod(self.config['grid_dims']), 2):
                raise ValueError(f"网格坐标形状错误：期望{(np.prod(self.config['grid_dims']), 2)}，"
                               f"实际{grid_centers_coords.shape}")
            
            # 生成预补偿核地图
            self.offline_stage.generate_prekernel_map(
                simulation_file=simulation_file,
                map_file=output_map_file,
                grid_dims=self.config['grid_dims'],
                grid_centers_highres_coords=grid_centers_coords,
                oversampling_rate=self.config['oversampling_rate'],
                matrix_size=self.config['matrix_size'],
                noise_r1=self.config['noise_r1'],
                noise_r2=self.config['noise_r2']
            )
            
            # 更新状态
            self.prekernel_map_file = Path(output_map_file)
            self.is_offline_complete = True
            
            elapsed_time = time.time() - start_time
            self.logger.info(f"=== 离线阶段完成，耗时 {elapsed_time:.2f} 秒 ===")
            
            return True
            
        except Exception as e:
            self.logger.error(f"离线阶段失败: {e}")
            return False
    
    def initialize_online_stage(self, map_file: Optional[Union[str, Path]] = None) -> bool:
        """
        初始化在线阶段
        
        参数:
            map_file: 核地图文件路径，如果为None则使用离线阶段生成的文件
            
        返回:
            在线阶段是否成功初始化
        """
        try:
            self.logger.info("=== 初始化在线阶段 ===")
            
            # 确定核地图文件路径
            if map_file is None:
                if not self.is_offline_complete or self.prekernel_map_file is None:
                    raise RuntimeError("离线阶段未完成，请先运行离线阶段或提供核地图文件")
                map_file = self.prekernel_map_file
            else:
                map_file = Path(map_file)
                if not map_file.exists():
                    raise FileNotFoundError(f"核地图文件不存在: {map_file}")
            
            # 加载预补偿核地图
            self.online_stage.load_prekernel_map(map_file)
            
            # 更新状态
            self.is_online_ready = True
            
            self.logger.info("=== 在线阶段初始化完成 ===")
            return True
            
        except Exception as e:
            self.logger.error(f"在线阶段初始化失败: {e}")
            return False
    
    def process_image(self, input_image: np.ndarray, 
                     output_path: Optional[Union[str, Path]] = None) -> np.ndarray:
        """
        处理单张图像 - 完全移植自MATLAB online_stage_auto
        
        对输入图像进行实时预补偿处理
        
        参数:
            input_image: 输入UI图像
            output_path: 输出路径（可选）
            
        返回:
            预补偿后的图像
        """
        if not self.is_online_ready:
            raise RuntimeError("在线阶段未初始化。请先调用initialize_online_stage()")
        
        try:
            self.logger.info("处理图像...")
            start_time = time.time()
            
            # 验证输入图像
            input_image = self._validate_input_image(input_image)
            
            # 应用预补偿
            compensated_image = self.online_stage.apply_compensation(input_image)
            
            # 后处理
            final_image = self._postprocess_image(compensated_image)
            
            # 保存结果
            if output_path:
                self._save_result(final_image, output_path)
            
            elapsed_time = time.time() - start_time
            self.logger.info(f"图像处理完成，耗时 {elapsed_time:.3f} 秒")
            return final_image
            
        except Exception as e:
            self.logger.error(f"图像处理失败: {e}")
            raise
    
    def process_batch(self, input_images: List[np.ndarray],
                     output_dir: Optional[Union[str, Path]] = None) -> List[np.ndarray]:
        """
        批量处理图像
        
        参数:
            input_images: 输入图像列表
            output_dir: 输出目录（可选）
            
        返回:
            预补偿后的图像列表
        """
        if not self.is_online_ready:
            raise RuntimeError("在线阶段未初始化。请先调用initialize_online_stage()")
        
        try:
            self.logger.info(f"开始批量处理 {len(input_images)} 张图像...")
            start_time = time.time()
            
            # 使用在线阶段的批量处理功能
            results = self.online_stage.process_image_batch(input_images, output_dir)
            
            elapsed_time = time.time() - start_time
            avg_time = elapsed_time / len(input_images)
            self.logger.info(f"批量处理完成，总耗时 {elapsed_time:.2f} 秒，"
                           f"平均每张 {avg_time:.3f} 秒")
            
            return results
            
        except Exception as e:
            self.logger.error(f"批量处理失败: {e}")
            raise
    
    def run_complete_workflow(self, 
                             simulation_file: Union[str, Path],
                             grid_centers_coords: np.ndarray,
                             input_images: Union[np.ndarray, List[np.ndarray]],
                             output_dir: Optional[Union[str, Path]] = None,
                             map_file: Optional[Union[str, Path]] = None) -> Union[np.ndarray, List[np.ndarray]]:
        """
        运行完整的工作流程 - 移植自MATLAB main_workflow
        
        从仿真数据到最终预补偿图像的端到端处理
        
        参数:
            simulation_file: 仿真数据文件路径
            grid_centers_coords: 网格中心坐标
            input_images: 输入图像或图像列表
            output_dir: 输出目录（可选）
            map_file: 核地图保存路径（可选，默认为临时文件）
            
        返回:
            预补偿后的图像或图像列表
        """
        try:
            self.logger.info("=== 开始完整工作流程 ===")
            overall_start_time = time.time()
            
            # 设置默认核地图文件路径
            if map_file is None:
                if self.cache_dir:
                    self.cache_dir.mkdir(parents=True, exist_ok=True)
                    map_file = self.cache_dir / "prekernel_map.mat"
                else:
                    map_file = Path("prekernel_map_temp.mat")
            
            # 步骤1：运行离线阶段
            success = self.run_offline_stage(simulation_file, grid_centers_coords, map_file)
            if not success:
                raise RuntimeError("离线阶段失败")
            
            # 步骤2：初始化在线阶段
            success = self.initialize_online_stage(map_file)
            if not success:
                raise RuntimeError("在线阶段初始化失败")
            
            # 步骤3：处理图像
            if isinstance(input_images, np.ndarray):
                # 单张图像
                output_file = None
                if output_dir:
                    output_dir = Path(output_dir)
                    output_dir.mkdir(parents=True, exist_ok=True)
                    output_file = output_dir / f"compensated.{self.config['output_format']}"
                
                result = self.process_image(input_images, output_file)
            else:
                # 多张图像
                result = self.process_batch(input_images, output_dir)
            
            overall_elapsed = time.time() - overall_start_time
            self.logger.info(f"=== 完整工作流程完成，总耗时 {overall_elapsed:.2f} 秒 ===")
            
            return result
            
        except Exception as e:
            self.logger.error(f"完整工作流程失败: {e}")
            raise
    
    def _validate_input_image(self, image: np.ndarray) -> np.ndarray:
        """验证输入图像 - 移植自MATLAB版本"""
        
        if not isinstance(image, np.ndarray):
            raise ValueError("输入必须是numpy数组")
        
        if image.ndim < 2 or image.ndim > 3:
            raise ValueError("图像必须是2D或3D数组")
        
        # 如果是彩色图像，转换为灰度
        if image.ndim == 3:
            if image.shape[2] == 3:  # RGB
                # 使用标准RGB权重转换为灰度
                image = 0.299 * image[:, :, 0] + 0.587 * image[:, :, 1] + 0.114 * image[:, :, 2]
            elif image.shape[2] == 4:  # RGBA
                # 忽略alpha通道
                image = 0.299 * image[:, :, 0] + 0.587 * image[:, :, 1] + 0.114 * image[:, :, 2]
            else:
                # 简单平均
                image = np.mean(image, axis=2)
        
        # 确保图像在[0, 1]范围内
        if image.max() > 1.0 or image.min() < 0.0:
            self.logger.warning("图像值不在[0,1]范围内，进行归一化")
            image = np.clip(image, 0, None)
            if image.max() > 0:
                image = image / image.max()
        
        # 检查无效值
        if np.any(np.isnan(image)) or np.any(np.isinf(image)):
            raise ValueError("输入图像包含NaN或Inf值")
        
        return image
    
    def _postprocess_image(self, image: np.ndarray) -> np.ndarray:
        """图像后处理 - 移植自MATLAB版本"""
        
        # 确保图像在合理范围内
        image = np.clip(image, 0, None)
        
        # 归一化到[0, 1]
        if image.max() > 0:
            image = image / image.max()
        
        # 去除噪声（可选）
        if self.config.get('denoise', False):
            from scipy.ndimage import gaussian_filter
            image = gaussian_filter(image, sigma=0.5)
        
        return image
    
    def _save_result(self, image: np.ndarray, output_path: Union[str, Path]) -> None:
        """保存结果图像"""
        
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            # 确保图像在[0, 1]范围内
            image_normalized = np.clip(image, 0, 1)
            
            # 转换为8位图像
            image_8bit = (image_normalized * 255).astype(np.uint8)
            
            # 保存图像
            if output_path.suffix.lower() in ['.png', '.jpg', '.jpeg', '.bmp', '.tiff']:
                from skimage.io import imsave
                imsave(str(output_path), image_8bit)
            elif output_path.suffix.lower() == '.mat':
                # 保存为MATLAB格式
                save_data = {'compensated_image': image_normalized}
                self.data_io.save_mat_file(output_path, save_data)
            else:
                # 默认使用PNG格式
                from skimage.io import imsave
                imsave(str(output_path.with_suffix('.png')), image_8bit)
            
            self.logger.info(f"结果已保存到: {output_path}")
            
        except Exception as e:
            self.logger.warning(f"保存图像失败: {e}")
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """
        获取处理统计信息
        
        返回:
            处理统计信息字典
        """
        stats = {
            'workflow_status': {
                'offline_complete': self.is_offline_complete,
                'online_ready': self.is_online_ready
            },
            'configuration': self.config.copy()
        }
        
        # 添加在线阶段统计信息
        if self.is_online_ready:
            online_stats = self.online_stage.get_processing_stats()
            stats['online_stage'] = online_stats
        
        return stats
    
    def reset(self) -> None:
        """重置工作流程状态"""
        
        self.is_offline_complete = False
        self.is_online_ready = False
        self.prekernel_map_file = None
        
        # 重新初始化组件
        self.offline_stage = OfflineStage()
        self.online_stage = OnlineStage()
        
        self.logger.info("工作流程状态已重置")


# 便捷函数
def run_complete_deconvolution(simulation_file: Union[str, Path],
                              grid_centers_coords: np.ndarray,
                              input_images: Union[np.ndarray, List[np.ndarray]],
                              config: Optional[Dict] = None,
                              output_dir: Optional[Union[str, Path]] = None) -> Union[np.ndarray, List[np.ndarray]]:
    """
    便捷函数：运行完整的去卷积工作流程
    
    与MATLAB main_workflow具有相同的接口。
    
    参数:
        simulation_file: 仿真数据文件路径
        grid_centers_coords: 网格中心坐标
        input_images: 输入图像或图像列表
        config: 配置参数（可选）
        output_dir: 输出目录（可选）
        
    返回:
        预补偿后的图像或图像列表
    """
    workflow = MainWorkflow(config)
    return workflow.run_complete_workflow(
        simulation_file, grid_centers_coords, input_images, output_dir) 