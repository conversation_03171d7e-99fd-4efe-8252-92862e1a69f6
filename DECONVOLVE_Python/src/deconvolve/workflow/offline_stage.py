"""
离线阶段工作流模块

完全移植自MATLAB offline_calculate_all_prekernels.m的功能。
实现从BSF数据生成预补偿核地图的完整离线处理流程。

主要功能：
- BSF数据加载和解析
- Wiener反卷积处理（BSF→PSF→PreKernel）
- 能量守恒下采样
- 坐标变换和验证
- 核地图生成和保存
"""

import numpy as np
import logging
from pathlib import Path
from typing import Tuple, Union
import warnings

from ..core.wiener_deconv import WienerDeconvolution
from ..core.image_processing import ImageProcessor
from ..io.data_io import DataIO
from ..utils.validation import ValidationUtils


class OfflineStage:
    """
    离线阶段处理器
    
    完全移植自MATLAB offline_calculate_all_prekernels.m的功能
    """
    
    def __init__(self):
        """初始化离线阶段处理器"""
        self.logger = logging.getLogger(__name__)
        self.wiener_deconv = WienerDeconvolution()
        self.image_processor = ImageProcessor()
        self.data_io = DataIO()
        self.validator = ValidationUtils()
    
    def generate_prekernel_map(self, 
                              simulation_file: Union[str, Path],
                              map_file: Union[str, Path],
                              grid_dims: Tuple[int, int],
                              grid_centers_highres_coords: np.ndarray,
                              oversampling_rate: int,
                              matrix_size: int,
                              noise_r1: float = 1e-5,
                              noise_r2: float = 1e-3) -> None:
        """
        生成预补偿核地图 - 完全移植自MATLAB offline_stage_auto
        
        从仿真数据生成预补偿核地图，用于实时图像处理。
        
        参数:
            simulation_file: 仿真数据文件路径
            map_file: 预补偿核地图输出文件
            grid_dims: 采样网格的维度 [rows, cols]
            grid_centers_highres_coords: 网格中心的高分辨率坐标
            oversampling_rate: 仿真与UI图像分辨率的比率
            matrix_size: BSF提取矩阵的大小
            noise_r1: BSF→PSF解卷积的噪声比率
            noise_r2: PSF→PreKernel解卷积的噪声比率
        """
        self.logger.info("开始离线阶段处理...")
        
        # 输入验证
        self._validate_inputs(simulation_file, grid_dims, grid_centers_highres_coords,
                             oversampling_rate, matrix_size, noise_r1, noise_r2)
        
        # 步骤1：解析原始数据获取BSF和坐标
        self.logger.info("步骤1：解析原始数据...")
        try:
            bsf_cells, precise_coords_highres = self.data_io.parse_speos_data(
                simulation_file, grid_centers_highres_coords, matrix_size)
        except Exception as e:
            raise RuntimeError(f"解析仿真数据失败: {e}")
        
        num_kernels = np.prod(grid_dims)
        prekernel_map_lowres = {}
        
        # 步骤2：定义源函数和目标函数
        self.logger.info("步骤2：定义源函数和目标函数...")
        
        # 创建矩形源函数（5×5像素，与文档一致）
        source_size = 5
        rect_source = np.ones((source_size, source_size)) / (source_size**2)
        
        # 创建delta目标函数（理想点源）
        delta_target = np.zeros((matrix_size, matrix_size))
        center_idx = matrix_size // 2
        delta_target[center_idx, center_idx] = 1
        
        # 步骤3：处理每个BSF生成预核
        self.logger.info(f"步骤3：处理{num_kernels}个BSF核...")
        
        for i in range(num_kernels):
            self.logger.info(f"处理核{i+1}/{num_kernels}...")
            
            # 获取归一化的BSF
            BSF_from_speos = bsf_cells[i]
            BSF_from_speos = BSF_from_speos / np.sum(BSF_from_speos)
            
            # 验证BSF数据
            if np.any(np.isnan(BSF_from_speos)) or np.any(np.isinf(BSF_from_speos)):
                self.logger.warning(f"BSF {i+1}包含NaN或Inf值。替换为零")
                BSF_from_speos = np.nan_to_num(BSF_from_speos, nan=0.0, posinf=0.0, neginf=0.0)
                BSF_from_speos = BSF_from_speos / max(np.sum(BSF_from_speos), np.finfo(float).eps)
            
            # 步骤3a：BSF → PSF转换，使用Wiener解卷积
            PSF_estimated = self.wiener_deconv.wiener_deconvolve(BSF_from_speos, rect_source, noise_r1)
            
            # 步骤3b：PSF → Pre-Kernel转换，使用Wiener解卷积
            pre_kernel_high_res = self.wiener_deconv.wiener_deconvolve(PSF_estimated, delta_target, noise_r2)
            
            # 验证预核
            if np.any(np.isnan(pre_kernel_high_res)) or np.any(np.isinf(pre_kernel_high_res)):
                self.logger.warning(f"预核{i+1}包含NaN或Inf值。使用恒等核")
                pre_kernel_high_res = delta_target.copy()
            
            # 步骤3c：下采样到低分辨率
            low_res_kernel = self.image_processor.downsample_kernel_energy_preserving(
                pre_kernel_high_res, oversampling_rate)
            
            # 存储到网格布局中
            row, col = np.unravel_index(i, grid_dims)
            prekernel_map_lowres[(row, col)] = low_res_kernel
        
        # 步骤4：坐标变换和验证
        self.logger.info("步骤4：坐标变换和验证...")
        
        # 将高分辨率仿真坐标转换为低分辨率UI图像坐标
        grid_centers_lowres_coords = precise_coords_highres / oversampling_rate
        
        # 全面的坐标验证
        self._validate_coordinates(grid_centers_lowres_coords, grid_dims, 
                                 oversampling_rate, precise_coords_highres)
        
        # 步骤5：保存结果
        self.logger.info("步骤5：保存结果...")
        try:
            # 转换为与MATLAB兼容的格式
            prekernel_map_cell = [[None for _ in range(grid_dims[1])] for _ in range(grid_dims[0])]
            for (row, col), kernel in prekernel_map_lowres.items():
                prekernel_map_cell[row][col] = kernel
            
            # 保存核地图
            save_data = {
                'prekernel_map_lowres': prekernel_map_cell,
                'grid_centers_lowres_coords': grid_centers_lowres_coords,
                'grid_dims': grid_dims
            }
            
            self.data_io.save_mat_file(map_file, save_data)
            self.logger.info(f'预补偿核地图已保存到"{map_file}"')
            
        except Exception as e:
            raise RuntimeError(f"保存核地图失败: {e}")
        
        self.logger.info("离线阶段处理完成")
    
    def _validate_inputs(self, simulation_file: Union[str, Path], 
                        grid_dims: Tuple[int, int],
                        grid_centers_coords: np.ndarray,
                        oversampling_rate: int, matrix_size: int,
                        noise_r1: float, noise_r2: float) -> None:
        """验证输入参数 - 移植自MATLAB版本"""
        
        simulation_file = Path(simulation_file)
        if not simulation_file.exists():
            raise FileNotFoundError(f'仿真文件不存在: {simulation_file}')
        
        if len(grid_dims) != 2 or any(d <= 0 for d in grid_dims):
            raise ValueError("grid_dims必须是包含正值的2元素向量")
        
        if oversampling_rate <= 0:
            raise ValueError("oversampling_rate必须为正数")
        
        if matrix_size <= 0 or not isinstance(matrix_size, int):
            raise ValueError("matrix_size必须是正整数")
        
        expected_num_points = np.prod(grid_dims)
        if len(grid_centers_coords) != expected_num_points:
            raise ValueError(f"期望{expected_num_points}个坐标点用于{grid_dims[0]}×{grid_dims[1]}网格，"
                           f"实际得到{len(grid_centers_coords)}个")
        
        if noise_r1 <= 0 or noise_r2 <= 0:
            raise ValueError("噪声比率必须为正数")
    
    def _validate_coordinates(self, grid_centers_lowres_coords: np.ndarray,
                             grid_dims: Tuple[int, int], oversampling_rate: int,
                             precise_coords_highres: np.ndarray) -> None:
        """
        全面的坐标验证 - 移植自MATLAB版本
        """
        self.logger.info("验证坐标变换...")
        
        # 检查无效值
        if np.any(np.isnan(grid_centers_lowres_coords)) or np.any(np.isinf(grid_centers_lowres_coords)):
            raise ValueError("变换后的坐标包含NaN或Inf值")
        
        # 检查坐标范围
        min_coords = np.min(grid_centers_lowres_coords, axis=0)
        max_coords = np.max(grid_centers_lowres_coords, axis=0)
        coord_range = max_coords - min_coords
        
        self.logger.info(f"坐标范围: X=[{min_coords[0]:.1f}, {max_coords[0]:.1f}], "
                        f"Y=[{min_coords[1]:.1f}, {max_coords[1]:.1f}]")
        
        # 警告潜在问题的坐标
        if np.any(grid_centers_lowres_coords <= 0):
            self.logger.warning("一些变换后的坐标非正数。这可能表示坐标系统问题")
            self.logger.info(f"最小坐标: X={min_coords[0]:.1f}, Y={min_coords[1]:.1f}")
        
        # 检查坐标是否形成合理的网格
        if grid_dims[1] > 1:
            expected_spacing_x = coord_range[0] / (grid_dims[1] - 1)
        else:
            expected_spacing_x = 0
            
        if grid_dims[0] > 1:
            expected_spacing_y = coord_range[1] / (grid_dims[0] - 1)
        else:
            expected_spacing_y = 0
        
        self.logger.info(f"期望网格间距: X={expected_spacing_x:.1f}, Y={expected_spacing_y:.1f}")
        
        # 验证网格规律性
        tolerance = 0.2  # 网格不规律性的20%容差
        for i in range(grid_dims[0]):
            for j in range(grid_dims[1]):
                idx = i * grid_dims[1] + j
                expected_x = min_coords[0] + j * expected_spacing_x
                expected_y = min_coords[1] + i * expected_spacing_y
                
                actual_x = grid_centers_lowres_coords[idx, 0]
                actual_y = grid_centers_lowres_coords[idx, 1]
                
                if expected_spacing_x > 0:
                    error_x = abs(actual_x - expected_x) / expected_spacing_x
                else:
                    error_x = 0
                    
                if expected_spacing_y > 0:
                    error_y = abs(actual_y - expected_y) / expected_spacing_y
                else:
                    error_y = 0
                
                if error_x > tolerance or error_y > tolerance:
                    self.logger.warning(f"网格点({i+1},{j+1})显著偏离期望位置。"
                                      f"误差: X={error_x*100:.1f}%, Y={error_y*100:.1f}%")
        
        # 检查坐标系统一致性
        if oversampling_rate != round(oversampling_rate):
            self.logger.warning(f"非整数过采样率({oversampling_rate:.2f})可能导致坐标对齐问题")
        
        # 验证坐标变换保持相对位置
        highres_range = np.max(precise_coords_highres, axis=0) - np.min(precise_coords_highres, axis=0)
        lowres_range = np.max(grid_centers_lowres_coords, axis=0) - np.min(grid_centers_lowres_coords, axis=0)
        
        # 避免除零
        scale_factors = np.where(lowres_range > np.finfo(float).eps, 
                               highres_range / lowres_range, 
                               oversampling_rate)
        
        scale_error = np.abs(scale_factors - oversampling_rate) / oversampling_rate
        if np.any(scale_error > 0.01):  # 1%容差
            self.logger.warning(f"检测到坐标缩放不一致。期望因子: {oversampling_rate:.1f}, "
                              f"实际: [{scale_factors[0]:.2f}, {scale_factors[1]:.2f}]")
        
        self.logger.info("坐标验证完成")
    
    def create_source_and_target_functions(self, matrix_size: int, 
                                          source_size: int = 5) -> Tuple[np.ndarray, np.ndarray]:
        """
        创建源函数和目标函数
        
        参数:
            matrix_size: 矩阵大小
            source_size: 源函数大小
            
        返回:
            (rect_source, delta_target): 矩形源函数和delta目标函数
        """
        # 创建矩形源函数（与文档一致的5×5像素）
        rect_source = np.ones((source_size, source_size)) / (source_size**2)
        
        # 创建delta目标函数（理想点源）
        delta_target = np.zeros((matrix_size, matrix_size))
        center_idx = matrix_size // 2
        delta_target[center_idx, center_idx] = 1
        
        return rect_source, delta_target
    
    def process_single_bsf(self, bsf_data: np.ndarray, rect_source: np.ndarray,
                          delta_target: np.ndarray, oversampling_rate: int,
                          noise_r1: float, noise_r2: float) -> np.ndarray:
        """
        处理单个BSF生成预核
        
        参数:
            bsf_data: BSF数据
            rect_source: 矩形源函数
            delta_target: delta目标函数
            oversampling_rate: 过采样率
            noise_r1: BSF→PSF噪声比率
            noise_r2: PSF→PreKernel噪声比率
            
        返回:
            低分辨率预核
        """
        # 归一化BSF
        bsf_normalized = bsf_data / np.sum(bsf_data)
        
        # BSF → PSF转换
        psf_estimated = self.wiener_deconv.wiener_deconvolve(bsf_normalized, rect_source, noise_r1)
        
        # PSF → Pre-Kernel转换
        pre_kernel_high_res = self.wiener_deconv.wiener_deconvolve(psf_estimated, delta_target, noise_r2)
        
        # 下采样到低分辨率
        low_res_kernel = self.image_processor.downsample_kernel_energy_preserving(
            pre_kernel_high_res, oversampling_rate)
        
        return low_res_kernel


# 便捷函数
def generate_prekernel_map(simulation_file: Union[str, Path],
                          map_file: Union[str, Path],
                          grid_dims: Tuple[int, int],
                          grid_centers_highres_coords: np.ndarray,
                          oversampling_rate: int,
                          matrix_size: int,
                          noise_r1: float = 1e-5,
                          noise_r2: float = 1e-3) -> None:
    """
    便捷函数：生成预补偿核地图
    
    与MATLAB函数offline_stage_auto具有相同的接口。
    """
    offline = OfflineStage()
    offline.generate_prekernel_map(
        simulation_file, map_file, grid_dims, grid_centers_highres_coords,
        oversampling_rate, matrix_size, noise_r1, noise_r2) 