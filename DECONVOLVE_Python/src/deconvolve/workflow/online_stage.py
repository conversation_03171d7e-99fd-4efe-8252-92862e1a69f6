"""
在线阶段工作流模块

完全移植自MATLAB online_apply_compensation.m的功能。
实现实时图像预补偿处理，使用预计算的核地图对UI图像进行空间变化补偿。

主要功能：
- 加载预补偿核地图
- 生成候选图像（卷积处理）
- 空间插值处理
- 实时图像补偿
"""

import numpy as np
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union, Any
import warnings
from scipy.ndimage import convolve

from ..core.interpolation import InterpolationEngine
from ..core.image_processing import ImageProcessor
from ..io.data_io import DataIO


class OnlineStage:
    """
    在线阶段处理器
    
    完全移植自MATLAB online_apply_compensation.m的功能
    """
    
    def __init__(self):
        """初始化在线阶段处理器"""
        self.logger = logging.getLogger(__name__)
        self.interpolation_engine = InterpolationEngine()
        self.image_processor = ImageProcessor()
        self.data_io = DataIO()
        
        # 缓存预补偿核地图
        self.prekernel_map = None
        self.grid_centers_coords = None
        self.is_initialized = False
    
    def load_prekernel_map(self, map_file: Union[str, Path]) -> None:
        """
        加载预补偿核地图
        
        参数:
            map_file: 核地图文件路径
        """
        map_file = Path(map_file)
        if not map_file.exists():
            raise FileNotFoundError(f"核地图文件不存在: {map_file}")
        
        try:
            self.logger.info(f"加载预计算核地图从: {map_file}")
            map_data = self.data_io.load_mat_file(map_file)
            
            # 验证加载的数据
            required_vars = ['prekernel_map_lowres', 'grid_centers_lowres_coords']
            for var in required_vars:
                if var not in map_data:
                    raise ValueError(f"核地图文件缺少必需变量: {var}")
            
            self.prekernel_map = map_data['prekernel_map_lowres']
            self.grid_centers_coords = map_data['grid_centers_lowres_coords']
            
            # 验证核地图格式
            self._validate_kernel_map()
            
            self.is_initialized = True
            self.logger.info("核地图加载成功")
            
        except Exception as e:
            raise RuntimeError(f"加载核地图失败: {e}")
    
    def apply_compensation(self, input_image: np.ndarray,
                          prekernel_map: Optional[Any] = None,
                          grid_centers_coords: Optional[np.ndarray] = None) -> np.ndarray:
        """
        应用预补偿 - 完全移植自MATLAB online_stage_auto
        
        对输入图像应用空间变化的预补偿处理。
        
        参数:
            input_image: 输入UI图像，2D numpy数组
            prekernel_map: 可选的预核地图，如果为None则使用已加载的
            grid_centers_coords: 可选的网格中心坐标
            
        返回:
            预补偿后的图像
        """
        # 使用提供的参数或已加载的数据
        if prekernel_map is not None:
            self.prekernel_map = prekernel_map
        if grid_centers_coords is not None:
            self.grid_centers_coords = grid_centers_coords
        
        # 验证初始化状态
        if not self.is_initialized and (self.prekernel_map is None or self.grid_centers_coords is None):
            raise RuntimeError("在线阶段未初始化。请先加载核地图或提供核地图参数")
        
        # 输入验证
        self._validate_inputs(input_image)
        
        img_h, img_w = input_image.shape[:2]
        
        # 将核地图转换为列表格式（如果是嵌套列表）
        kernel_list, num_kernels = self._process_kernel_map()
        
        self.logger.info(f"对{img_h}×{img_w}图像应用预补偿，使用{num_kernels}个核")
        
        # 步骤1：生成候选图像
        self.logger.info("生成候选图像...")
        candidate_images = self._generate_candidate_images(input_image, kernel_list)
        
        # 步骤2：空间插值
        self.logger.info("执行空间插值...")
        precompensated_image = self._perform_spatial_interpolation(
            candidate_images, self.grid_centers_coords)
        
        self.logger.info("预补偿处理完成")
        return precompensated_image
    
    def _validate_inputs(self, input_image: np.ndarray) -> None:
        """验证输入参数 - 移植自MATLAB版本"""
        
        if not isinstance(input_image, np.ndarray):
            raise ValueError("input_image必须是numpy数组")
        
        if input_image.ndim < 2 or input_image.ndim > 3:
            raise ValueError("input_image必须是2D或3D数组")
        
        # 如果是彩色图像，转换为灰度
        if input_image.ndim == 3:
            self.logger.info("检测到彩色图像，转换为灰度")
            # 简单的灰度转换
            input_image = np.mean(input_image, axis=2)
        
        if np.any(np.isnan(input_image)) or np.any(np.isinf(input_image)):
            raise ValueError("输入图像包含NaN或Inf值")
        
        if input_image.size == 0:
            raise ValueError("输入图像为空")
    
    def _validate_kernel_map(self) -> None:
        """验证核地图数据 - 移植自MATLAB版本"""
        
        # 检查核地图格式
        if isinstance(self.prekernel_map, list):
            # 嵌套列表格式
            grid_h = len(self.prekernel_map)
            grid_w = len(self.prekernel_map[0]) if grid_h > 0 else 0
            num_kernels = grid_h * grid_w
        else:
            # 其他格式（字典等）
            num_kernels = len(self.prekernel_map)
        
        # 验证坐标数量
        if len(self.grid_centers_coords) != num_kernels:
            raise ValueError(f"坐标数量({len(self.grid_centers_coords)})与核数量({num_kernels})不匹配")
        
        # 检查坐标有效性
        if np.any(np.isnan(self.grid_centers_coords)) or np.any(np.isinf(self.grid_centers_coords)):
            raise ValueError("网格坐标包含NaN或Inf值")
        
        self.logger.info(f"核地图验证通过：{num_kernels}个核")
    
    def _process_kernel_map(self) -> Tuple[List[np.ndarray], int]:
        """
        处理核地图，转换为统一的列表格式
        
        返回:
            (kernel_list, num_kernels): 核列表和核数量
        """
        kernel_list = []
        
        if isinstance(self.prekernel_map, list):
            # 嵌套列表格式（来自MATLAB cell数组）
            for row in self.prekernel_map:
                if isinstance(row, list):
                    for kernel in row:
                        if kernel is not None:
                            kernel_list.append(np.array(kernel))
                else:
                    # 单层列表
                    if row is not None:
                        kernel_list.append(np.array(row))
        
        elif isinstance(self.prekernel_map, dict):
            # 字典格式
            for key in sorted(self.prekernel_map.keys()):
                kernel_list.append(np.array(self.prekernel_map[key]))
        
        else:
            raise ValueError("不支持的核地图格式")
        
        # 验证每个核
        for i, kernel in enumerate(kernel_list):
            if not isinstance(kernel, np.ndarray) or kernel.ndim != 2:
                raise ValueError(f"核{i+1}必须是2D数值矩阵")
            
            if np.any(np.isnan(kernel)) or np.any(np.isinf(kernel)):
                raise ValueError(f"核{i+1}包含NaN或Inf值")
        
        return kernel_list, len(kernel_list)
    
    def _generate_candidate_images(self, input_image: np.ndarray, 
                                  kernel_list: List[np.ndarray]) -> np.ndarray:
        """
        生成候选图像 - 移植自MATLAB版本
        
        对输入图像与每个预补偿核进行卷积，生成候选图像集合。
        这是"先卷积，后插值"策略的第一步。
        
        参数:
            input_image: 输入图像
            kernel_list: 预补偿核列表
            
        返回:
            候选图像数组，形状为(H, W, N)
        """
        img_h, img_w = input_image.shape
        num_kernels = len(kernel_list)
        candidate_images = np.zeros((img_h, img_w, num_kernels))
        
        # 检查是否可以使用并行处理（简化版本，不依赖joblib）
        self.logger.info(f"生成{num_kernels}个候选图像...")
        
        # 串行处理每个核
        for i, kernel in enumerate(kernel_list):
            try:
                # 使用scipy.ndimage.convolve进行卷积
                # 对应MATLAB的imfilter(input_image, kernel, 'replicate', 'conv', 'same')
                candidate_images[:, :, i] = convolve(
                    input_image, 
                    kernel, 
                    mode='reflect',  # 对应MATLAB的'replicate'
                    cval=0.0
                )
            except Exception as e:
                self.logger.warning(f"核{i+1}的卷积处理失败: {e}")
                # 失败时使用原图像
                candidate_images[:, :, i] = input_image
        
        return candidate_images
    
    def _perform_spatial_interpolation(self, candidate_images: np.ndarray,
                                     grid_centers_coords: np.ndarray) -> np.ndarray:
        """
        执行空间插值 - 移植自MATLAB版本
        
        在候选图像之间进行空间插值，生成最终的预补偿图像。
        这是"先卷积，后插值"策略的第二步。
        
        参数:
            candidate_images: 候选图像数组，形状为(H, W, N)
            grid_centers_coords: 网格中心坐标，形状为(N, 2)
            
        返回:
            插值后的预补偿图像
        """
        # 使用优化的空间插值
        try:
            result = self.interpolation_engine.optimized_spatial_interpolation(
                candidate_images, grid_centers_coords, method='linear')
        except Exception as e:
            self.logger.warning(f"优化插值失败，回退到标准插值: {e}")
            # 回退到标准空间插值
            result = self.interpolation_engine.spatial_varying_interpolation(
                candidate_images, grid_centers_coords, method='linear')
        
        return result
    
    def process_image_batch(self, image_list: List[np.ndarray],
                           output_dir: Optional[Union[str, Path]] = None) -> List[np.ndarray]:
        """
        批量处理图像
        
        参数:
            image_list: 输入图像列表
            output_dir: 可选的输出目录
            
        返回:
            预补偿后的图像列表
        """
        if not self.is_initialized:
            raise RuntimeError("在线阶段未初始化。请先加载核地图")
        
        results = []
        total = len(image_list)
        
        output_dir = Path(output_dir) if output_dir else None
        if output_dir:
            output_dir.mkdir(parents=True, exist_ok=True)
        
        for i, image in enumerate(image_list):
            self.logger.info(f"处理图像{i+1}/{total}...")
            
            try:
                result = self.apply_compensation(image)
                results.append(result)
                
                # 可选：保存结果
                if output_dir:
                    output_file = output_dir / f"compensated_{i:04d}.png"
                    self._save_image(result, output_file)
                    
            except Exception as e:
                self.logger.error(f"处理图像{i+1}失败: {e}")
                # 失败时使用原图像
                results.append(image)
        
        return results
    
    def _save_image(self, image: np.ndarray, filepath: Union[str, Path]) -> None:
        """保存图像"""
        try:
            from skimage.io import imsave
            # 确保图像在[0, 1]范围内
            image_normalized = np.clip(image, 0, 1)
            imsave(str(filepath), (image_normalized * 255).astype(np.uint8))
        except Exception as e:
            self.logger.warning(f"保存图像失败: {e}")
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """
        获取处理统计信息
        
        返回:
            处理统计信息字典
        """
        if not self.is_initialized:
            return {'status': 'not_initialized'}
        
        kernel_list, num_kernels = self._process_kernel_map()
        
        # 计算核统计信息
        kernel_sizes = [kernel.shape for kernel in kernel_list]
        kernel_energies = [np.sum(kernel) for kernel in kernel_list]
        
        stats = {
            'status': 'initialized',
            'num_kernels': num_kernels,
            'kernel_sizes': kernel_sizes,
            'kernel_energies': kernel_energies,
            'grid_coords_range': {
                'x_min': np.min(self.grid_centers_coords[:, 0]),
                'x_max': np.max(self.grid_centers_coords[:, 0]),
                'y_min': np.min(self.grid_centers_coords[:, 1]),
                'y_max': np.max(self.grid_centers_coords[:, 1])
            }
        }
        
        return stats


# 便捷函数
def apply_compensation(input_image: np.ndarray,
                      prekernel_map: Any,
                      grid_centers_coords: np.ndarray) -> np.ndarray:
    """
    便捷函数：应用预补偿
    
    与MATLAB函数online_stage_auto具有相同的接口。
    
    参数:
        input_image: 输入图像
        prekernel_map: 预核地图
        grid_centers_coords: 网格中心坐标
        
    返回:
        预补偿后的图像
    """
    online = OnlineStage()
    return online.apply_compensation(input_image, prekernel_map, grid_centers_coords)


def load_and_apply_compensation(input_image: np.ndarray,
                              map_file: Union[str, Path]) -> np.ndarray:
    """
    便捷函数：加载核地图并应用补偿
    
    参数:
        input_image: 输入图像
        map_file: 核地图文件路径
        
    返回:
        预补偿后的图像
    """
    online = OnlineStage()
    online.load_prekernel_map(map_file)
    return online.apply_compensation(input_image) 