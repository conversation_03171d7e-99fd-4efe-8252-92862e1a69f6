"""
导入测试 - 确保所有模块可以正常导入
"""

import unittest
import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))


class TestImports(unittest.TestCase):
    """测试所有主要模块的导入"""
    
    def test_main_package_import(self):
        """测试主包导入"""
        try:
            import deconvolve
            self.assertTrue(hasattr(deconvolve, '__version__'))
        except ImportError as e:
            self.fail(f"主包导入失败: {e}")
    
    def test_core_modules_import(self):
        """测试核心模块导入"""
        try:
            from deconvolve.core import wiener_deconv, image_processing, interpolation
            from deconvolve.core.wiener_deconv import WienerDeconvolution
            from deconvolve.core.image_processing import ImageProcessor
        except ImportError as e:
            self.fail(f"核心模块导入失败: {e}")
    
    def test_io_modules_import(self):
        """测试IO模块导入"""
        try:
            from deconvolve.io import DataIO, load_bsf_data
            from deconvolve.io.data_io import DataIO as DataIOClass
        except ImportError as e:
            self.fail(f"IO模块导入失败: {e}")
    
    def test_workflow_modules_import(self):
        """测试工作流模块导入"""
        try:
            from deconvolve.workflow import MainWorkflow, OfflineStage, OnlineStage
            from deconvolve.workflow import generate_prekernel_map, apply_compensation
        except ImportError as e:
            self.fail(f"工作流模块导入失败: {e}")
    
    def test_utils_modules_import(self):
        """测试工具模块导入"""
        try:
            from deconvolve.utils import validation
            from deconvolve.utils.validation import ValidationUtils
        except ImportError as e:
            self.fail(f"工具模块导入失败: {e}")
    
    def test_main_api_functions(self):
        """测试主要API函数导入"""
        try:
            from deconvolve import (
                generate_prekernel_map,
                apply_compensation,
                load_bsf_data,
                parse_speos_data,
                wiener_deconvolve
            )
        except ImportError as e:
            self.fail(f"主要API函数导入失败: {e}")


if __name__ == '__main__':
    unittest.main() 