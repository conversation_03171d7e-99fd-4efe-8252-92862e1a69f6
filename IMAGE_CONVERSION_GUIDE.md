# 图像转换功能使用指南

## 概述

根据您的需求，我已经为HUD预补偿工作流添加了完整的图像识别和转换功能。该功能可以将图像转换为BSF数据格式，并集成到现有的处理流程中。

## 基于您提供的示例代码

您提供的示例代码：
```matlab
image = imread("/Users/<USER>/Files From d.localized/工作文档/代码/TRAE示例/deconvolve_matlab/sim_images/postsim_image.png");
rgbimage = image(:,:,1:3);
grayimage = rgb2gray(rgbimage);
figure; imagesc(grayimage); colormap("gray"); colorbar;
```

我已经将这个处理流程完全集成到了新的转换功能中。

## 新增的功能文件

### 1. `image_to_bsf_converter.m`
- **功能**: 将图像转换为单个BSF文件格式
- **输出**: 保存为 `bsf_0.mat` 到 `bsf_data` 文件夹
- **用法**: 
  ```matlab
  image_to_bsf_converter('sim_images/postsim_image.png')
  image_to_bsf_converter('sim_images/postsim_image.png', 'bsf_data', 'bsf_0')
  ```

### 2. `convert_image_to_speos_format.m`
- **功能**: 将图像转换为大型SPEOS导出文件格式
- **输出**: 生成文本文件，包含 X, Y, Intensity 列
- **用法**:
  ```matlab
  convert_image_to_speos_format('sim_images/postsim_image.png')
  convert_image_to_speos_format('sim_images/postsim_image.png', 'my_speos_data.txt')
  ```

### 3. `image_processing_workflow.m`
- **功能**: 综合的图像处理工作流
- **模式**: 
  - `'demo'` - 演示模式
  - `'bsf'` - BSF格式转换
  - `'speos'` - SPEOS格式转换
- **用法**:
  ```matlab
  image_processing_workflow('demo')
  image_processing_workflow('bsf', 'sim_images/postsim_image.png')
  image_processing_workflow('speos', 'sim_images/postsim_image.png')
  ```

### 4. `test_image_conversion.m`
- **功能**: 测试所有图像转换功能
- **用法**: 直接运行测试脚本
  ```matlab
  test_image_conversion
  ```

## 集成到主工作流

我已经修改了 `main_workflow.m`，添加了图像转换选项：

```matlab
% 在 main_workflow.m 中设置这些参数
convert_image_to_bsf = true;  % 启用图像转换
image_conversion_path = 'sim_images/postsim_image.png';  % 图像路径
converted_bsf_name = 'bsf_0';  % 转换后的BSF文件名
```

## 使用步骤

### 方法1: 直接转换为BSF格式
```matlab
% 1. 转换图像
image_to_bsf_converter('sim_images/postsim_image.png', 'bsf_data', 'bsf_0');

% 2. 运行主工作流
main_workflow
```

### 方法2: 转换为SPEOS格式
```matlab
% 1. 转换图像
convert_image_to_speos_format('sim_images/postsim_image.png', 'large_speos_sample.txt');

% 2. 运行主工作流（会自动检测大型SPEOS文件）
main_workflow
```

### 方法3: 集成工作流
```matlab
% 1. 编辑 main_workflow.m，设置：
%    convert_image_to_bsf = true;
%    image_conversion_path = 'sim_images/postsim_image.png';

% 2. 运行主工作流
main_workflow
```

## 图像处理流程

所有转换功能都遵循您提供的示例代码流程：

1. **读取图像**: `image = imread(image_path)`
2. **提取RGB通道**: `rgbimage = image(:,:,1:3)`
3. **转换为灰度**: `grayimage = rgb2gray(rgbimage)`
4. **显示结果**: `figure; imagesc(grayimage); colormap("gray"); colorbar`
5. **保存为BSF格式**: 归一化并保存为 `.mat` 文件

## 输出文件

### BSF模式输出：
- `bsf_data/bsf_0.mat` - BSF数据文件
- `bsf_data/bsf_0_visualization.png` - 可视化图像

### SPEOS模式输出：
- `large_speos_sample.txt` - SPEOS格式数据文件
- `large_speos_sample_coordinates.txt` - 网格坐标文件

## 测试和验证

运行测试脚本验证所有功能：
```matlab
test_image_conversion
```

该脚本会：
1. 测试演示模式
2. 执行您提供的示例代码
3. 测试BSF转换
4. 测试SPEOS转换
5. 提供集成指导

## 支持的图像格式

- PNG, JPEG, TIFF, BMP 等常见格式
- RGB 和灰度图像
- 自动处理不同通道数的图像

## 注意事项

1. **图像路径**: 确保图像文件存在且路径正确
2. **输出目录**: 会自动创建 `bsf_data` 目录
3. **数据归一化**: BSF数据会自动归一化为概率分布（总和为1）
4. **坐标系统**: SPEOS格式会自动生成适当的坐标系统

## 下一步

转换完成后，您可以：
1. 使用转换后的数据运行完整的HUD预补偿工作流
2. 查看生成的预补偿核地图
3. 应用实时图像补偿
4. 评估补偿效果

所有功能都已经完全集成到现有的工作流中，可以无缝使用。
