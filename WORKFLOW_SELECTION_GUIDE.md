# HUD Pre-Compensation Workflow Selection Guide

## Overview

This codebase provides two complementary workflows for HUD optical pre-compensation. Choose the appropriate workflow based on your needs, experience level, and data availability.

## Workflow Comparison

### 🔧 **main_workflow.m** - Comprehensive Workflow
**Best for:** Advanced users, research applications, complex scenarios

**Features:**
- ✅ Supports multiple BSF files (bsf_1.mat to bsf_36.mat)
- ✅ Manual coordinate configuration for precise control
- ✅ Large SPEOS file processing (>100MB files)
- ✅ Full feature set with all processing options
- ✅ Backward compatibility with existing data
- ✅ Integrated image conversion option
- ✅ 6x6 grid processing for comprehensive coverage

**Use Cases:**
- Research and development
- Complex optical systems
- Multiple measurement points
- Large-scale simulation data
- Custom coordinate configurations

### ⚡ **simplified_workflow.m** - Simplified Workflow
**Best for:** Basic users, quick processing, single image scenarios

**Features:**
- ✅ Single image input (no multiple BSF files needed)
- ✅ Automatic coordinate configuration
- ✅ Streamlined user interface
- ✅ Beginner-friendly operation
- ✅ Text-based data format processing
- ✅ Faster processing for simple cases

**Use Cases:**
- Quick prototyping
- Single image processing
- Educational purposes
- Simple optical systems
- Automated processing pipelines

## Resolution Handling System

### Dual-Folder Architecture
Both workflows use the same dual-resolution system:

```
sim_images/     # High-resolution simulation results (reference images)
├── postsim_image.png
└── ...

ui_images/      # Target resolution UI images (for compensation)
├── test_arrow.png
└── ...
```

### Resolution Scaling Process
1. **High-Resolution Processing** (sim_images)
   - Images processed at simulation resolution
   - BSF extraction and kernel generation
   - Coordinate calculation in high-res space

2. **Resolution Downsampling**
   - `oversampling_rate = 3` (configurable)
   - High-res kernels → Low-res kernels (÷3)
   - High-res coordinates → Low-res coordinates (÷3)
   - Energy-preserving downsampling

3. **Target Resolution Application** (ui_images)
   - Pre-compensation applied at target resolution
   - Matches UI image resolution
   - Real-time processing capability

## Decision Matrix

| Criteria | main_workflow.m | simplified_workflow.m |
|----------|-----------------|----------------------|
| **Ease of Use** | ⭐⭐ | ⭐⭐⭐⭐⭐ |
| **Processing Speed** | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **Feature Completeness** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |
| **Flexibility** | ⭐⭐⭐⭐⭐ | ⭐⭐ |
| **Data Requirements** | Multiple files | Single image |
| **Setup Complexity** | High | Low |
| **Learning Curve** | Steep | Gentle |

## Quick Start Guide

### For main_workflow.m:
```matlab
% 1. Prepare your data (choose one):
%    - Multiple BSF files: bsf_1.mat to bsf_36.mat in bsf_data/
%    - Large SPEOS file: large_speos_sample.txt
%    - Single image: enable convert_image_to_bsf = true

% 2. Configure coordinates (if using BSF files):
%    - Set grid_centers_highres_coords in main_workflow.m

% 3. Run the workflow:
main_workflow
```

### For simplified_workflow.m:
```matlab
% 1. Place your image in sim_images/ folder

% 2. Run the workflow (that's it!):
simplified_workflow
```

## File Organization

### Core Files You Need:
```
main_workflow.m              # Comprehensive workflow
simplified_workflow.m        # Simplified workflow
offline_stage_auto.m         # Core processing functions
online_stage_auto.m          # Core processing functions
helper_*.m                   # Supporting utilities
```

### Data Folders:
```
sim_images/                  # High-resolution reference images
ui_images/                   # Target resolution UI images
bsf_data/                    # BSF data files (for main_workflow)
image_data/                  # Converted image data (for simplified_workflow)
```

## Migration Path

### From main_workflow.m to simplified_workflow.m:
1. Extract a representative image from your BSF data
2. Place it in sim_images/ folder
3. Run simplified_workflow.m
4. Compare results with original workflow

### From simplified_workflow.m to main_workflow.m:
1. Generate multiple BSF files using your simulation software
2. Configure grid coordinates manually
3. Run main_workflow.m for full processing

## Troubleshooting

### Common Issues:

**main_workflow.m:**
- Missing BSF files → Check bsf_data/ folder
- Coordinate mismatch → Verify grid_centers_highres_coords
- Large file processing → Ensure sufficient memory

**simplified_workflow.m:**
- Image not found → Check sim_images/ folder
- Conversion errors → Verify image format compatibility
- Poor results → Consider using main_workflow.m for better control

## Performance Considerations

### main_workflow.m:
- Processing time: 2-10 minutes (depending on data size)
- Memory usage: High (multiple BSF files)
- Accuracy: Highest (full grid processing)

### simplified_workflow.m:
- Processing time: 30 seconds - 2 minutes
- Memory usage: Low (single image)
- Accuracy: Good (single point processing)

## Conclusion

Choose **main_workflow.m** for:
- Research and development
- Maximum accuracy and control
- Complex optical systems
- Multiple measurement points

Choose **simplified_workflow.m** for:
- Quick prototyping
- Educational purposes
- Simple optical systems
- Automated processing

Both workflows are actively maintained and can coexist in the same codebase without conflicts.
