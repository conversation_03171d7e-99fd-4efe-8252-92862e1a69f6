function grid_centers_coords = auto_coordinate_config(data_source, mode)
    % AUTO_COORDINATE_CONFIG - Automatic coordinate system configuration
    %
    % This function replaces manual grid_centers_highres_coords input with
    % automatic coordinate configuration based on data analysis.
    %
    % Inputs:
    %   data_source - Path to data file or coordinate file
    %   mode - Configuration mode:
    %          'single' - Single image processing (default)
    %          'centroid' - Use data centroid
    %          'file' - Read from coordinate file
    %
    % Outputs:
    %   grid_centers_coords - Automatically configured coordinates
    %
    % Usage:
    %   coords = auto_coordinate_config('image_data/converted_image_coordinates.txt')
    %   coords = auto_coordinate_config('image_data/converted_image_data.txt', 'centroid')

    if nargin < 1
        error('Data source is required.');
    end
    
    if nargin < 2 || isempty(mode)
        mode = 'single';
    end

    fprintf('=== Automatic Coordinate Configuration ===\n');
    fprintf('Data source: %s\n', data_source);
    fprintf('Mode: %s\n', mode);

    % Check if data source exists
    if ~exist(data_source, 'file')
        error('Data source "%s" does not exist.', data_source);
    end

    try
        switch lower(mode)
            case 'single'
                grid_centers_coords = config_single_image(data_source);
                
            case 'centroid'
                grid_centers_coords = config_from_centroid(data_source);
                
            case 'file'
                grid_centers_coords = config_from_file(data_source);
                
            otherwise
                error('Unknown mode: %s. Valid modes are: single, centroid, file', mode);
        end
        
        % Validate output
        if isempty(grid_centers_coords) || size(grid_centers_coords, 2) ~= 2
            error('Invalid coordinate configuration generated.');
        end
        
        fprintf('Generated coordinate configuration:\n');
        fprintf('  Number of points: %d\n', size(grid_centers_coords, 1));
        fprintf('  X range: [%.2f, %.2f]\n', min(grid_centers_coords(:,1)), max(grid_centers_coords(:,1)));
        fprintf('  Y range: [%.2f, %.2f]\n', min(grid_centers_coords(:,2)), max(grid_centers_coords(:,2)));
        
        fprintf('=== Coordinate configuration completed ===\n');

    catch ME
        fprintf('ERROR in coordinate configuration: %s\n', ME.message);
        rethrow(ME);
    end
end

function coords = config_single_image(data_source)
    % Configure coordinates for single image processing
    
    fprintf('  - Configuring for single image processing...\n');
    
    % Check if this is a coordinate file or data file
    if contains(data_source, 'coordinates')
        % Read from coordinate file
        coords = read_coordinate_file(data_source);
    else
        % Calculate centroid from data file
        coords = calculate_centroid_from_data(data_source);
    end
    
    % For single image processing, we use a simplified approach
    % Instead of 36 grid points, we use the image center or centroid
    fprintf('  - Using single-point coordinate configuration\n');
end

function coords = config_from_centroid(data_source)
    % Configure coordinates based on data centroid calculation

    fprintf('  - Calculating centroid from data...\n');

    try
        % Read data file with improved error handling
        try
            data = readmatrix(data_source, 'CommentStyle', '%');
        catch
            % Fallback: manual parsing
            data = manual_read_data(data_source);
        end

        % Remove any NaN rows
        data = data(~any(isnan(data), 2), :);

        if isempty(data)
            error('No valid data found in file');
        end

        if size(data, 2) < 3
            error('Data file must have at least 3 columns (X, Y, Intensity), found %d columns', size(data, 2));
        end

        X = data(:, 1);
        Y = data(:, 2);
        I = data(:, 3);

        % Calculate weighted centroid
        total_intensity = sum(I);
        if total_intensity > eps
            centroid_x = sum(X .* I) / total_intensity;
            centroid_y = sum(Y .* I) / total_intensity;
        else
            % Fallback to geometric center
            centroid_x = mean(X);
            centroid_y = mean(Y);
        end

        coords = [centroid_x, centroid_y];

        fprintf('  - Calculated centroid: (%.2f, %.2f)\n', centroid_x, centroid_y);

    catch ME
        error('Failed to calculate centroid: %s', ME.message);
    end
end

function coords = config_from_file(data_source)
    % Read coordinates directly from file
    
    fprintf('  - Reading coordinates from file...\n');
    coords = read_coordinate_file(data_source);
end

function coords = read_coordinate_file(coord_file)
    % Read coordinate data from file

    try
        % Try readmatrix first
        try
            coords = readmatrix(coord_file, 'CommentStyle', '%');
        catch
            % Fallback: manual parsing for better compatibility
            coords = manual_read_coordinates(coord_file);
        end

        % Remove any NaN rows (empty lines)
        coords = coords(~any(isnan(coords), 2), :);

        if isempty(coords)
            error('No valid coordinate data found in file');
        end

        if size(coords, 2) ~= 2
            error('Coordinate file must have exactly 2 columns (X, Y), found %d columns', size(coords, 2));
        end

        fprintf('  - Read %d coordinate points from file\n', size(coords, 1));

    catch ME
        error('Failed to read coordinate file: %s', ME.message);
    end
end

function coords = manual_read_coordinates(coord_file)
    % Manual coordinate file parsing for better compatibility

    fid = fopen(coord_file, 'r');
    if fid == -1
        error('Cannot open coordinate file: %s', coord_file);
    end

    coords = [];
    line_num = 0;

    try
        while ~feof(fid)
            line = fgetl(fid);
            line_num = line_num + 1;

            if ischar(line)
                % Skip comment lines and empty lines
                line = strtrim(line);
                if isempty(line) || startsWith(line, '%')
                    continue;
                end

                % Parse numeric data
                nums = str2num(line); %#ok<ST2NM>
                if length(nums) >= 2
                    coords = [coords; nums(1), nums(2)]; %#ok<AGROW>
                end
            end
        end

    catch ME
        fclose(fid);
        error('Error parsing line %d: %s', line_num, ME.message);
    end

    fclose(fid);

    if isempty(coords)
        error('No coordinate data found in file');
    end
end

function coords = calculate_centroid_from_data(data_file)
    % Calculate centroid from data file

    try
        % Read data file with improved error handling
        try
            data = readmatrix(data_file, 'CommentStyle', '%');
        catch
            % Fallback: manual parsing
            data = manual_read_data(data_file);
        end

        % Remove any NaN rows
        data = data(~any(isnan(data), 2), :);

        if isempty(data)
            error('No valid data found in file');
        end

        if size(data, 2) < 3
            error('Data file must have at least 3 columns (X, Y, Intensity), found %d columns', size(data, 2));
        end

        X = data(:, 1);
        Y = data(:, 2);
        I = data(:, 3);

        % Calculate weighted centroid
        total_intensity = sum(I);
        if total_intensity > eps
            centroid_x = sum(X .* I) / total_intensity;
            centroid_y = sum(Y .* I) / total_intensity;
        else
            % Fallback to geometric center
            centroid_x = mean(X);
            centroid_y = mean(Y);
        end

        coords = [centroid_x, centroid_y];

        fprintf('  - Calculated centroid from data: (%.2f, %.2f)\n', centroid_x, centroid_y);

    catch ME
        error('Failed to calculate centroid from data: %s', ME.message);
    end
end

function data = manual_read_data(data_file)
    % Manual data file parsing for better compatibility

    fid = fopen(data_file, 'r');
    if fid == -1
        error('Cannot open data file: %s', data_file);
    end

    data = [];
    line_num = 0;

    try
        while ~feof(fid)
            line = fgetl(fid);
            line_num = line_num + 1;

            if ischar(line)
                % Skip comment lines and empty lines
                line = strtrim(line);
                if isempty(line) || startsWith(line, '%')
                    continue;
                end

                % Parse numeric data
                nums = str2num(line); %#ok<ST2NM>
                if length(nums) >= 3
                    data = [data; nums(1), nums(2), nums(3)]; %#ok<AGROW>
                end
            end
        end

    catch ME
        fclose(fid);
        error('Error parsing line %d: %s', line_num, ME.message);
    end

    fclose(fid);

    if isempty(data)
        error('No data found in file');
    end
end
