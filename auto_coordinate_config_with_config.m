function grid_centers_coords = auto_coordinate_config_with_config(data_source, mode, cfg)
    % AUTO_COORDINATE_CONFIG_WITH_CONFIG - Config-aware coordinate configuration
    %
    % This function extends the original auto_coordinate_config with unified
    % configuration system support, providing enhanced fallback mechanisms
    % and consistent parameter usage.
    %
    % Inputs:
    %   data_source - Path to data file or coordinate file
    %   mode - Configuration mode ('single', 'centroid', 'file')
    %   cfg - Configuration structure from config.m
    %
    % Outputs:
    %   grid_centers_coords - Automatically configured coordinates

    fprintf('=== Automatic Coordinate Configuration (Config-Aware) ===\n');
    fprintf('Data source: %s\n', data_source);
    fprintf('Mode: %s\n', mode);
    fprintf('Config fallback available: [%.2f, %.2f]\n', ...
        cfg.simplified_workflow.fallback_coordinates(1), cfg.simplified_workflow.fallback_coordinates(2));

    % Check if data source exists
    if ~exist(data_source, 'file')
        fprintf('Data source "%s" does not exist. Using config fallback.\n', data_source);
        grid_centers_coords = cfg.simplified_workflow.fallback_coordinates;
        return;
    end

    try
        switch lower(mode)
            case 'single'
                grid_centers_coords = config_single_image_with_config(data_source, cfg);
                
            case 'centroid'
                grid_centers_coords = config_from_centroid_with_config(data_source, cfg);
                
            case 'file'
                grid_centers_coords = config_from_file_with_config(data_source, cfg);
                
            otherwise
                warning('Unknown mode: %s. Using single mode.', mode);
                grid_centers_coords = config_single_image_with_config(data_source, cfg);
        end
        
        % Validate output
        if isempty(grid_centers_coords) || size(grid_centers_coords, 2) ~= 2
            warning('Invalid coordinate configuration generated. Using config fallback.');
            grid_centers_coords = cfg.simplified_workflow.fallback_coordinates;
        end
        
        fprintf('Generated coordinate configuration:\n');
        fprintf('  Number of points: %d\n', size(grid_centers_coords, 1));
        fprintf('  X range: [%.2f, %.2f]\n', min(grid_centers_coords(:,1)), max(grid_centers_coords(:,1)));
        fprintf('  Y range: [%.2f, %.2f]\n', min(grid_centers_coords(:,2)), max(grid_centers_coords(:,2)));
        
        fprintf('=== Coordinate configuration completed ===\n');

    catch ME
        fprintf('ERROR in coordinate configuration: %s\n', ME.message);
        fprintf('Using config fallback coordinates.\n');
        grid_centers_coords = cfg.simplified_workflow.fallback_coordinates;
    end
end

function coords = config_single_image_with_config(data_source, cfg)
    % Configure coordinates for single image processing with config support
    
    fprintf('  - Configuring for single image processing (config-aware)...\n');
    
    % Check if this is a coordinate file or data file
    if contains(data_source, 'coordinates')
        % Read from coordinate file
        coords = read_coordinate_file_with_config(data_source, cfg);
    else
        % Calculate centroid from data file
        coords = calculate_centroid_from_data_with_config(data_source, cfg);
    end
    
    % For single image processing, we use a simplified approach
    fprintf('  - Using single-point coordinate configuration\n');
end

function coords = config_from_centroid_with_config(data_source, cfg)
    % Configure coordinates based on data centroid calculation with config support
    
    fprintf('  - Calculating centroid from data (config-aware)...\n');
    
    try
        % Read data file with improved error handling
        try
            data = readmatrix(data_source, 'CommentStyle', '%');
        catch
            % Fallback: manual parsing
            data = manual_read_data_with_config(data_source, cfg);
        end
        
        % Remove any NaN rows
        data = data(~any(isnan(data), 2), :);
        
        if isempty(data)
            warning('No valid data found in file. Using config fallback.');
            coords = cfg.simplified_workflow.fallback_coordinates;
            return;
        end
        
        if size(data, 2) < 3
            warning('Data file must have at least 3 columns. Using config fallback.');
            coords = cfg.simplified_workflow.fallback_coordinates;
            return;
        end
        
        X = data(:, 1);
        Y = data(:, 2);
        I = data(:, 3);
        
        % Calculate weighted centroid
        total_intensity = sum(I);
        if total_intensity > eps
            centroid_x = sum(X .* I) / total_intensity;
            centroid_y = sum(Y .* I) / total_intensity;
        else
            % Fallback to geometric center
            centroid_x = mean(X);
            centroid_y = mean(Y);
        end
        
        coords = [centroid_x, centroid_y];
        
        fprintf('  - Calculated centroid: (%.2f, %.2f)\n', centroid_x, centroid_y);
        
    catch ME
        warning('Failed to calculate centroid: %s. Using config fallback.', ME.message);
        coords = cfg.simplified_workflow.fallback_coordinates;
    end
end

function coords = config_from_file_with_config(data_source, cfg)
    % Read coordinates directly from file with config support
    
    fprintf('  - Reading coordinates from file (config-aware)...\n');
    coords = read_coordinate_file_with_config(data_source, cfg);
end

function coords = read_coordinate_file_with_config(coord_file, cfg)
    % Read coordinate data from file with config fallback support
    
    try
        % Try readmatrix first
        try
            coords = readmatrix(coord_file, 'CommentStyle', '%');
        catch
            % Fallback: manual parsing for better compatibility
            coords = manual_read_coordinates_with_config(coord_file, cfg);
        end
        
        % Remove any NaN rows (empty lines)
        coords = coords(~any(isnan(coords), 2), :);
        
        if isempty(coords)
            warning('No valid coordinate data found in file. Using config fallback.');
            coords = cfg.simplified_workflow.fallback_coordinates;
            return;
        end
        
        if size(coords, 2) ~= 2
            warning('Coordinate file must have exactly 2 columns (X, Y), found %d. Using config fallback.', size(coords, 2));
            coords = cfg.simplified_workflow.fallback_coordinates;
            return;
        end
        
        fprintf('  - Read %d coordinate points from file\n', size(coords, 1));
        
    catch ME
        warning('Failed to read coordinate file: %s. Using config fallback.', ME.message);
        coords = cfg.simplified_workflow.fallback_coordinates;
    end
end

function coords = manual_read_coordinates_with_config(coord_file, cfg)
    % Manual coordinate file parsing with config fallback
    
    fid = fopen(coord_file, 'r');
    if fid == -1
        warning('Cannot open coordinate file: %s. Using config fallback.', coord_file);
        coords = cfg.simplified_workflow.fallback_coordinates;
        return;
    end
    
    coords = [];
    line_num = 0;
    
    try
        while ~feof(fid)
            line = fgetl(fid);
            line_num = line_num + 1;
            
            if ischar(line)
                % Skip comment lines and empty lines
                line = strtrim(line);
                if isempty(line) || startsWith(line, '%')
                    continue;
                end
                
                % Parse numeric data
                nums = str2num(line); %#ok<ST2NM>
                if length(nums) >= 2
                    coords = [coords; nums(1), nums(2)]; %#ok<AGROW>
                end
            end
        end
        
    catch ME
        fclose(fid);
        warning('Error parsing line %d: %s. Using config fallback.', line_num, ME.message);
        coords = cfg.simplified_workflow.fallback_coordinates;
        return;
    end
    
    fclose(fid);
    
    if isempty(coords)
        warning('No coordinate data found in file. Using config fallback.');
        coords = cfg.simplified_workflow.fallback_coordinates;
    end
end

function coords = calculate_centroid_from_data_with_config(data_file, cfg)
    % Calculate centroid from data file with config fallback
    
    try
        % Read data file with improved error handling
        try
            data = readmatrix(data_file, 'CommentStyle', '%');
        catch
            % Fallback: manual parsing
            data = manual_read_data_with_config(data_file, cfg);
        end
        
        % Remove any NaN rows
        data = data(~any(isnan(data), 2), :);
        
        if isempty(data)
            warning('No valid data found in file. Using config fallback.');
            coords = cfg.simplified_workflow.fallback_coordinates;
            return;
        end
        
        if size(data, 2) < 3
            warning('Data file must have at least 3 columns. Using config fallback.');
            coords = cfg.simplified_workflow.fallback_coordinates;
            return;
        end
        
        X = data(:, 1);
        Y = data(:, 2);
        I = data(:, 3);
        
        % Calculate weighted centroid
        total_intensity = sum(I);
        if total_intensity > eps
            centroid_x = sum(X .* I) / total_intensity;
            centroid_y = sum(Y .* I) / total_intensity;
        else
            % Fallback to geometric center
            centroid_x = mean(X);
            centroid_y = mean(Y);
        end
        
        coords = [centroid_x, centroid_y];
        
        fprintf('  - Calculated centroid from data: (%.2f, %.2f)\n', centroid_x, centroid_y);
        
    catch ME
        warning('Failed to calculate centroid from data: %s. Using config fallback.', ME.message);
        coords = cfg.simplified_workflow.fallback_coordinates;
    end
end

function data = manual_read_data_with_config(data_file, cfg)
    % Manual data file parsing with config fallback
    
    fid = fopen(data_file, 'r');
    if fid == -1
        warning('Cannot open data file: %s. Using config fallback.', data_file);
        % Return empty data to trigger fallback
        data = [];
        return;
    end
    
    data = [];
    line_num = 0;
    
    try
        while ~feof(fid)
            line = fgetl(fid);
            line_num = line_num + 1;
            
            if ischar(line)
                % Skip comment lines and empty lines
                line = strtrim(line);
                if isempty(line) || startsWith(line, '%')
                    continue;
                end
                
                % Parse numeric data
                nums = str2num(line); %#ok<ST2NM>
                if length(nums) >= 3
                    data = [data; nums(1), nums(2), nums(3)]; %#ok<AGROW>
                end
            end
        end
        
    catch ME
        fclose(fid);
        warning('Error parsing line %d: %s', line_num, ME.message);
        data = [];  % Return empty to trigger fallback
        return;
    end
    
    fclose(fid);
    
    if isempty(data)
        warning('No data found in file.');
    end
end
