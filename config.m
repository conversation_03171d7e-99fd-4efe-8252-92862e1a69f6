function config_struct = config(config_name, custom_params)
    % CONFIG - Unified configuration system for HUD pre-compensation workflows
    %
    % This function provides centralized parameter management for both
    % main_workflow.m and simplified_workflow.m, ensuring consistency
    % and easy parameter tuning across the entire system.
    %
    % Usage:
    %   config_struct = config()                    % Default configuration
    %   config_struct = config('research')          % Research preset
    %   config_struct = config('fast')              % Fast processing preset
    %   config_struct = config('custom', params)    % Custom parameters
    %
    % Available Presets:
    %   'default'   - Balanced performance and accuracy
    %   'fast'      - Optimized for speed
    %   'research'  - Maximum accuracy for research
    %   'simple'    - Simplified workflow optimized
    %   'custom'    - User-defined parameters
    %
    % Example:
    %   cfg = config('research');
    %   main_workflow_with_config(cfg);

    if nargin < 1
        config_name = 'default';
    end
    
    if nargin < 2
        custom_params = struct();
    end

    % Base configuration structure
    config_struct = struct();
    
    %% === CORE MATRIX AND SAMPLING PARAMETERS ===
    
    % Original dot matrix dimensions (6x6 grid system)
    config_struct.grid_dims = [6, 6];              % [rows, cols] - 36 BSF points
    
    % Individual square spot pixel size (BSF extraction)
    config_struct.matrix_size = 31;                % Odd number preferred (31x31)
    
    % Imaging plane sampling (dual-resolution system)
    config_struct.oversampling_rate = 3;           % sim_images:ui_images ratio
    
    %% === ALGORITHM TUNING PARAMETERS ===
    
    % Wiener deconvolution noise regularization
    config_struct.noise_ratio_step1 = 1e-5;        % BSF → PSF conversion
    config_struct.noise_ratio_step2 = 1e-3;        % PSF → PreKernel conversion
    
    % Source function configuration
    config_struct.source_size = 5;                 % Rectangular source (5x5)
    config_struct.source_type = 'rectangular';     % 'rectangular' or 'gaussian'
    
    %% === FILE PROCESSING PARAMETERS ===
    
    % Data filtering and validation
    config_struct.intensity_threshold = 0.001;     % Minimum intensity for data points
    config_struct.large_file_threshold_mb = 100;   % MB threshold for enhanced processing
    config_struct.min_points_per_bsf = 3;          % Minimum data points per BSF
    
    % Memory management
    config_struct.chunk_size_mb = 50;              % Processing chunk size (MB)
    config_struct.region_overlap_factor = 0.2;     % 20% overlap between regions
    
    %% === QUALITY CONTROL PARAMETERS ===
    
    % Signal quality thresholds
    config_struct.snr_warning_threshold = 5;       % SNR below this triggers warning
    config_struct.energy_conservation_tolerance = 1e-10;  % Energy conservation check
    config_struct.coordinate_precision_threshold = 1.0;   % Coordinate accuracy limit
    
    % Validation settings
    config_struct.enable_quality_validation = true;
    config_struct.enable_progress_display = true;
    config_struct.enable_intermediate_saves = false;
    
    %% === WORKFLOW-SPECIFIC PARAMETERS ===
    
    % Main workflow settings
    config_struct.main_workflow = struct();
    config_struct.main_workflow.enable_image_conversion = false;
    config_struct.main_workflow.auto_detect_data_source = true;
    config_struct.main_workflow.save_intermediate_results = false;
    
    % Simplified workflow settings
    config_struct.simplified_workflow = struct();
    config_struct.simplified_workflow.auto_coordinate_config = true;
    config_struct.simplified_workflow.fallback_coordinates = [150, 100];
    config_struct.simplified_workflow.single_kernel_mode = true;
    
    %% === FILE PATH CONFIGURATION ===
    
    % Input directories
    config_struct.paths = struct();
    config_struct.paths.sim_images_dir = 'sim_images';
    config_struct.paths.ui_images_dir = 'ui_images';
    config_struct.paths.bsf_data_dir = 'bsf_data';
    config_struct.paths.output_dir = 'output';
    config_struct.paths.temp_dir = 'temp';
    
    % Default file names
    config_struct.files = struct();
    config_struct.files.default_ui_image = 'test_arrow.png';
    config_struct.files.kernel_map_output = 'prekernel_map_lowres_final.mat';
    config_struct.files.precompensated_output = 'precompensated_image_final.png';
    
    %% === APPLY PRESET CONFIGURATIONS ===
    
    switch lower(config_name)
        case 'default'
            % Already set above - balanced configuration
            
        case 'fast'
            % Optimized for speed
            config_struct.grid_dims = [4, 4];              % Fewer BSF points
            config_struct.matrix_size = 21;                % Smaller matrices
            config_struct.oversampling_rate = 2;           % Lower oversampling
            config_struct.noise_ratio_step1 = 1e-4;        % Less precise deconvolution
            config_struct.noise_ratio_step2 = 1e-2;
            config_struct.chunk_size_mb = 100;             % Larger chunks
            config_struct.enable_quality_validation = false; % Skip validation
            
        case 'research'
            % Maximum accuracy for research applications
            config_struct.grid_dims = [8, 8];              % More BSF points
            config_struct.matrix_size = 51;                % Larger matrices
            config_struct.oversampling_rate = 4;           % Higher oversampling
            config_struct.noise_ratio_step1 = 1e-6;        % More precise deconvolution
            config_struct.noise_ratio_step2 = 1e-4;
            config_struct.intensity_threshold = 0.0001;    % Lower threshold
            config_struct.chunk_size_mb = 25;              % Smaller chunks for precision
            config_struct.enable_intermediate_saves = true; % Save intermediate results
            
        case 'simple'
            % Optimized for simplified workflow
            config_struct.grid_dims = [1, 1];              % Single BSF
            config_struct.matrix_size = 31;                % Standard size
            config_struct.oversampling_rate = 3;           % Standard oversampling
            config_struct.noise_ratio_step1 = 1e-3;        % Simplified workflow values
            config_struct.noise_ratio_step2 = 1e-3;
            config_struct.simplified_workflow.auto_coordinate_config = true;
            config_struct.enable_progress_display = true;
            
        case 'custom'
            % Apply custom parameters
            field_names = fieldnames(custom_params);
            for i = 1:length(field_names)
                config_struct.(field_names{i}) = custom_params.(field_names{i});
            end
            
        otherwise
            warning('Unknown configuration preset: %s. Using default.', config_name);
    end
    
    %% === PARAMETER VALIDATION ===
    
    config_struct = validate_config(config_struct);
    
    %% === DISPLAY CONFIGURATION SUMMARY ===
    
    if config_struct.enable_progress_display
        display_config_summary(config_struct, config_name);
    end
end

function config_struct = validate_config(config_struct)
    % Validate configuration parameters and fix invalid values
    
    % Validate grid dimensions
    if any(config_struct.grid_dims <= 0) || length(config_struct.grid_dims) ~= 2
        warning('Invalid grid_dims. Using default [6, 6].');
        config_struct.grid_dims = [6, 6];
    end
    
    % Validate matrix size (should be odd for symmetric kernels)
    if config_struct.matrix_size <= 0 || mod(config_struct.matrix_size, 1) ~= 0
        warning('Invalid matrix_size. Using default 31.');
        config_struct.matrix_size = 31;
    elseif mod(config_struct.matrix_size, 2) == 0
        warning('matrix_size should be odd for symmetric kernels. Adding 1.');
        config_struct.matrix_size = config_struct.matrix_size + 1;
    end
    
    % Validate oversampling rate
    if config_struct.oversampling_rate <= 0
        warning('Invalid oversampling_rate. Using default 3.');
        config_struct.oversampling_rate = 3;
    end
    
    % Validate noise ratios
    if config_struct.noise_ratio_step1 <= 0 || config_struct.noise_ratio_step1 >= 1
        warning('Invalid noise_ratio_step1. Using default 1e-5.');
        config_struct.noise_ratio_step1 = 1e-5;
    end
    
    if config_struct.noise_ratio_step2 <= 0 || config_struct.noise_ratio_step2 >= 1
        warning('Invalid noise_ratio_step2. Using default 1e-3.');
        config_struct.noise_ratio_step2 = 1e-3;
    end
    
    % Validate thresholds
    if config_struct.intensity_threshold < 0 || config_struct.intensity_threshold > 1
        warning('Invalid intensity_threshold. Using default 0.001.');
        config_struct.intensity_threshold = 0.001;
    end
    
    % Add validation timestamp
    config_struct.validation_timestamp = datestr(now);
end

function display_config_summary(config_struct, config_name)
    % Display configuration summary
    
    fprintf('\n=== HUD Pre-Compensation Configuration ===\n');
    fprintf('Preset: %s\n', config_name);
    fprintf('Validation: %s\n', config_struct.validation_timestamp);
    
    fprintf('\n--- Core Parameters ---\n');
    fprintf('Grid dimensions: %dx%d (%d BSF points)\n', ...
        config_struct.grid_dims(1), config_struct.grid_dims(2), prod(config_struct.grid_dims));
    fprintf('Matrix size: %dx%d\n', config_struct.matrix_size, config_struct.matrix_size);
    fprintf('Oversampling rate: %dx\n', config_struct.oversampling_rate);
    
    fprintf('\n--- Algorithm Parameters ---\n');
    fprintf('Noise ratio (BSF→PSF): %.1e\n', config_struct.noise_ratio_step1);
    fprintf('Noise ratio (PSF→PreKernel): %.1e\n', config_struct.noise_ratio_step2);
    fprintf('Source function: %dx%d %s\n', config_struct.source_size, config_struct.source_size, config_struct.source_type);
    
    fprintf('\n--- Quality Control ---\n');
    fprintf('Intensity threshold: %.4f\n', config_struct.intensity_threshold);
    fprintf('SNR warning threshold: %.1f\n', config_struct.snr_warning_threshold);
    fprintf('Quality validation: %s\n', logical_to_string(config_struct.enable_quality_validation));
    
    fprintf('=====================================\n\n');
end

function str = logical_to_string(logical_val)
    if logical_val
        str = 'Enabled';
    else
        str = 'Disabled';
    end
end
