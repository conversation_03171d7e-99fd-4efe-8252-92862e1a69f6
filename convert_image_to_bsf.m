function convert_image_to_bsf(image_path, output_folder, bsf_name)
    % CONVERT_IMAGE_TO_BSF - Convert image to BSF data format for processing
    %
    % This function reads an image, converts it to grayscale, and saves it
    % in the BSF data format that can be processed by the main workflow.
    %
    % Inputs:
    %   image_path - Path to the input image file (optional - uses default if not provided)
    %   output_folder - Output folder for BSF data (default: 'bsf_data')
    %   bsf_name - Name for the BSF file (default: 'bsf_0')
    %
    % Usage:
    %   convert_image_to_bsf()  % Uses default image
    %   convert_image_to_bsf('sim_images/postsim_image.png')
    %   convert_image_to_bsf('sim_images/postsim_image.png', 'bsf_data', 'bsf_0')

    % Set default parameters
    if nargin < 1 || isempty(image_path)
        % Try to find a default image
        if exist('ui_images/postsim_image.png', 'file')
            image_path = 'ui_images/postsim_image.png';
            fprintf('Using default image: %s\n', image_path);
        else
            error('No image path provided and no default image found. Please provide an image path.');
        end
    end
    if nargin < 2
        output_folder = 'bsf_data';
    end
    if nargin < 3
        bsf_name = 'bsf_0';
    end

    fprintf('=== Image to BSF Conversion ===\n');
    fprintf('Converting image: %s\n', image_path);

    % Check if input image exists
    if ~exist(image_path, 'file')
        error('Image file "%s" does not exist.', image_path);
    end

    % Create output directory if it doesn't exist
    if ~exist(output_folder, 'dir')
        mkdir(output_folder);
        fprintf('Created output directory: %s\n', output_folder);
    end

    try
        % Read the image
        fprintf('Reading image...\n');
        image = imread(image_path);
        
        % Display image information
        fprintf('Image size: %dx%dx%d\n', size(image, 1), size(image, 2), size(image, 3));
        fprintf('Image class: %s\n', class(image));

        % Convert to RGB if necessary (handle different image formats)
        if size(image, 3) == 4
            % RGBA image - take only RGB channels
            rgbimage = image(:,:,1:3);
            fprintf('Converted RGBA to RGB\n');
        elseif size(image, 3) == 3
            % Already RGB
            rgbimage = image;
        elseif size(image, 3) == 1
            % Grayscale image - convert to RGB for consistency
            rgbimage = repmat(image, [1, 1, 3]);
            fprintf('Converted grayscale to RGB\n');
        else
            error('Unsupported image format with %d channels', size(image, 3));
        end

        % Convert RGB to grayscale
        fprintf('Converting to grayscale...\n');
        grayimage = rgb2gray(rgbimage);

        % Convert to double and normalize to [0, 1]
        bsf_data = double(grayimage) / 255.0;

        % Normalize to unit sum (BSF should be a probability distribution)
        bsf_data = bsf_data / sum(bsf_data(:));

        % Display statistics
        fprintf('BSF data statistics:\n');
        fprintf('  Size: %dx%d\n', size(bsf_data, 1), size(bsf_data, 2));
        fprintf('  Min value: %.6f\n', min(bsf_data(:)));
        fprintf('  Max value: %.6f\n', max(bsf_data(:)));
        fprintf('  Sum: %.6f\n', sum(bsf_data(:)));

        % Save as .mat file in BSF format
        output_file = fullfile(output_folder, [bsf_name '.mat']);
        save(output_file, 'bsf_data');
        fprintf('BSF data saved to: %s\n', output_file);

        % Display the grayscale image
        figure('Name', 'Converted BSF Data');
        imagesc(bsf_data);
        colormap('gray');
        colorbar;
        title(sprintf('BSF Data from %s', image_path));
        xlabel('X coordinate');
        ylabel('Y coordinate');

        % Save the visualization
        vis_file = fullfile(output_folder, [bsf_name '_visualization.png']);
        saveas(gcf, vis_file);
        fprintf('Visualization saved to: %s\n', vis_file);

        fprintf('Conversion completed successfully!\n');

    catch ME
        fprintf('ERROR during conversion: %s\n', ME.message);
        rethrow(ME);
    end
end
