function convert_image_to_speos_format(image_path, output_file, grid_dims, image_scale_factor)
    % CONVERT_IMAGE_TO_SPEOS_FORMAT - Convert image to large SPEOS export format
    %
    % This function converts an image to a format that mimics large SPEOS export files
    % with X, Y, Intensity columns, which can then be processed by the existing
    % large SPEOS file processing workflow.
    %
    % Inputs:
    %   image_path - Path to the input image file
    %   output_file - Output text file path (default: 'large_speos_sample.txt')
    %   grid_dims - Grid dimensions [rows, cols] (default: [6, 6])
    %   image_scale_factor - Scale factor for coordinates (default: 100)
    %
    % Usage:
    %   convert_image_to_speos_format('sim_images/postsim_image.png')
    %   convert_image_to_speos_format('sim_images/postsim_image.png', 'my_speos_data.txt')
    %
    % The function creates a text file with format:
    %   X_coordinate Y_coordinate Intensity
    %   (one line per pixel with non-zero intensity)

    % Set default parameters
    if nargin < 1
        error('Image path is required.');
    end
    
    if nargin < 2 || isempty(output_file)
        output_file = 'large_speos_sample.txt';
    end
    
    if nargin < 3 || isempty(grid_dims)
        grid_dims = [6, 6];
    end
    
    if nargin < 4 || isempty(image_scale_factor)
        image_scale_factor = 100;  % Scale coordinates to match typical SPEOS data
    end

    fprintf('=== Image to SPEOS Format Conversion ===\n');
    fprintf('Converting image: %s\n', image_path);
    fprintf('Output file: %s\n', output_file);

    % Check if input image exists
    if ~exist(image_path, 'file')
        error('Image file "%s" does not exist.', image_path);
    end

    try
        % Read and process the image (following the provided example)
        fprintf('Reading and processing image...\n');
        image = imread(image_path);
        
        % Display image information
        fprintf('Original image size: %dx%dx%d\n', size(image, 1), size(image, 2), size(image, 3));

        % Convert to RGB if necessary
        if size(image, 3) >= 3
            rgbimage = image(:,:,1:3);
        elseif size(image, 3) == 1
            rgbimage = repmat(image, [1, 1, 3]);
        else
            error('Unsupported image format with %d channels', size(image, 3));
        end

        % Convert RGB to grayscale (following the example)
        grayimage = rgb2gray(rgbimage);
        
        % Convert to double and normalize
        intensity_data = double(grayimage) / 255.0;
        
        % Get image dimensions
        [img_height, img_width] = size(intensity_data);
        
        % Create coordinate grids
        [X, Y] = meshgrid(1:img_width, 1:img_height);
        
        % Scale coordinates to match typical SPEOS coordinate system
        X_scaled = X * image_scale_factor / img_width;
        Y_scaled = Y * image_scale_factor / img_height;
        
        % Flatten the arrays
        X_flat = X_scaled(:);
        Y_flat = Y_scaled(:);
        I_flat = intensity_data(:);
        
        % Filter out very low intensity values to reduce file size
        intensity_threshold = 0.001;  % Adjust as needed
        valid_indices = I_flat > intensity_threshold;
        
        X_filtered = X_flat(valid_indices);
        Y_filtered = Y_flat(valid_indices);
        I_filtered = I_flat(valid_indices);
        
        fprintf('Filtered data points: %d (from %d total pixels)\n', ...
            length(X_filtered), length(X_flat));
        
        % Write to text file in SPEOS format
        fprintf('Writing SPEOS format file...\n');
        fid = fopen(output_file, 'w');
        if fid == -1
            error('Cannot create output file: %s', output_file);
        end
        
        % Write header comment (optional)
        fprintf(fid, '%% SPEOS-like data generated from image: %s\n', image_path);
        fprintf(fid, '%% Format: X_coordinate Y_coordinate Intensity\n');
        fprintf(fid, '%% Total data points: %d\n', length(X_filtered));
        
        % Write data
        for i = 1:length(X_filtered)
            fprintf(fid, '%.6f %.6f %.6f\n', X_filtered(i), Y_filtered(i), I_filtered(i));
        end
        
        fclose(fid);
        
        % Display statistics
        fprintf('SPEOS format conversion statistics:\n');
        fprintf('  Output file: %s\n', output_file);
        fprintf('  Data points written: %d\n', length(X_filtered));
        fprintf('  X coordinate range: [%.3f, %.3f]\n', min(X_filtered), max(X_filtered));
        fprintf('  Y coordinate range: [%.3f, %.3f]\n', min(Y_filtered), max(Y_filtered));
        fprintf('  Intensity range: [%.6f, %.6f]\n', min(I_filtered), max(I_filtered));
        
        % Create coordinate file for the grid centers
        coord_file = strrep(output_file, '.txt', '_coordinates.txt');
        fprintf('Creating coordinate file: %s\n', coord_file);
        
        % Generate grid center coordinates
        [grid_x, grid_y] = meshgrid(0:grid_dims(2)-1, 0:grid_dims(1)-1);
        grid_centers = [grid_x(:) * image_scale_factor / grid_dims(2) + image_scale_factor/(2*grid_dims(2)), ...
                       grid_y(:) * image_scale_factor / grid_dims(1) + image_scale_factor/(2*grid_dims(1))];
        
        % Write coordinate file
        fid_coord = fopen(coord_file, 'w');
        if fid_coord ~= -1
            fprintf(fid_coord, '%% Grid center coordinates for %dx%d grid\n', grid_dims(1), grid_dims(2));
            for i = 1:size(grid_centers, 1)
                fprintf(fid_coord, '%.6f %.6f\n', grid_centers(i, 1), grid_centers(i, 2));
            end
            fclose(fid_coord);
            fprintf('Coordinate file saved: %s\n', coord_file);
        end
        
        % Display the converted image
        figure('Name', 'Image to SPEOS Format Conversion');
        subplot(1, 2, 1);
        imagesc(grayimage);
        colormap('gray');
        colorbar;
        title('Original Grayscale Image');
        xlabel('X coordinate');
        ylabel('Y coordinate');
        
        subplot(1, 2, 2);
        scatter(X_filtered, Y_filtered, 1, I_filtered, 'filled');
        colormap('gray');
        colorbar;
        title('SPEOS Format Data Points');
        xlabel('X coordinate (scaled)');
        ylabel('Y coordinate (scaled)');
        axis equal;
        
        fprintf('=== Conversion completed successfully! ===\n');
        fprintf('Next steps:\n');
        fprintf('1. The SPEOS format file has been saved as: %s\n', output_file);
        fprintf('2. Grid coordinates saved as: %s\n', coord_file);
        fprintf('3. You can now use this in the main workflow by setting:\n');
        fprintf('   data_source = ''%s''\n', output_file);
        fprintf('4. The workflow will automatically detect this as a large SPEOS file\n');

    catch ME
        fprintf('ERROR during conversion: %s\n', ME.message);
        rethrow(ME);
    end
end
