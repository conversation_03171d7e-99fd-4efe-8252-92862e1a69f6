% CREATE_SAMPLE_DATA - Generate sample BSF data and test image for HUD system
%
% This script creates sample data files to test the HUD pre-compensation workflow:
% 1. Sample BSF data files (bsf_1.mat to bsf_36.mat) in bsf_data/ directory
% 2. Sample UI test image in ui_images/ directory
%
% The generated BSF data simulates realistic optical blur patterns that vary
% across a 6x6 grid, representing different field positions in the HUD.

fprintf('Creating sample data for HUD pre-compensation system...\n');

% Parameters
grid_dims = [6, 6];
matrix_size = 31;
num_kernels = prod(grid_dims);

% Create directories if they don't exist
if ~exist('bsf_data', 'dir')
    mkdir('bsf_data');
end
if ~exist('ui_images', 'dir')
    mkdir('ui_images');
end

%% Generate Sample BSF Data
fprintf('Generating %d sample BSF files...\n', num_kernels);

% Create a base Gaussian PSF
[X, Y] = meshgrid(1:matrix_size, 1:matrix_size);
center = (matrix_size + 1) / 2;

for i = 1:num_kernels
    % Calculate grid position
    [row, col] = ind2sub(grid_dims, i);
    
    % Vary blur characteristics based on position
    % Simulate field-dependent aberrations
    sigma_x = 1.5 + 0.5 * abs(col - 3.5);  % Horizontal blur varies with column
    sigma_y = 1.5 + 0.3 * abs(row - 3.5);  % Vertical blur varies with row
    
    % Add some astigmatism (different blur in X and Y)
    rotation = (row - 3.5) * (col - 3.5) * 0.1;  % Slight rotation based on position
    
    % Create anisotropic Gaussian
    X_rot = (X - center) * cos(rotation) - (Y - center) * sin(rotation);
    Y_rot = (X - center) * sin(rotation) + (Y - center) * cos(rotation);
    
    bsf_data = exp(-(X_rot.^2 / (2 * sigma_x^2) + Y_rot.^2 / (2 * sigma_y^2)));
    
    % Add some noise and asymmetry to make it more realistic
    noise_level = 0.05;
    bsf_data = bsf_data + noise_level * randn(size(bsf_data));
    bsf_data = max(bsf_data, 0);  % Ensure non-negative
    
    % Add slight asymmetry (coma-like aberration)
    coma_strength = 0.1 * sqrt((row - 3.5)^2 + (col - 3.5)^2);
    coma_shift = coma_strength * [(col - 3.5), (row - 3.5)] / 3.5;
    
    if abs(coma_shift(1)) > 0.1 || abs(coma_shift(2)) > 0.1
        [X_shift, Y_shift] = meshgrid(1:matrix_size, 1:matrix_size);
        X_shift = X_shift - coma_shift(1);
        Y_shift = Y_shift - coma_shift(2);
        
        % Interpolate to apply shift
        bsf_shifted = interp2(X, Y, bsf_data, X_shift, Y_shift, 'linear', 0);
        bsf_data = 0.7 * bsf_data + 0.3 * bsf_shifted;
    end
    
    % Normalize
    bsf_data = bsf_data / sum(bsf_data(:));
    
    % Save to file
    filename = sprintf('bsf_data/bsf_%d.mat', i);
    save(filename, 'bsf_data');
    
    if mod(i, 6) == 0
        fprintf('  Generated %d/%d BSF files...\n', i, num_kernels);
    end
end

fprintf('Sample BSF data generation complete.\n');

%% Generate Sample UI Image
fprintf('Generating sample UI test image...\n');

% Create a test image with various features
img_size = [200, 300];  % Height x Width
test_image = zeros(img_size);

% Add some geometric shapes
% Arrow pointing right
arrow_center = [100, 150];
arrow_points = [
    -30, -10;
    -30, 10;
    -10, 10;
    -10, 20;
    30, 0;
    -10, -20;
    -10, -10
];

% Scale and translate arrow points
arrow_points = arrow_points + arrow_center;

% Create arrow using polygon
[X, Y] = meshgrid(1:img_size(2), 1:img_size(1));
arrow_mask = inpolygon(X, Y, arrow_points(:,1), arrow_points(:,2));
test_image(arrow_mask) = 1;

% Add some text-like rectangular features
rectangles = [
    50, 50, 20, 80;   % y, x, height, width
    50, 200, 20, 60;
    150, 80, 15, 100;
];

for i = 1:size(rectangles, 1)
    y = rectangles(i, 1);
    x = rectangles(i, 2);
    h = rectangles(i, 3);
    w = rectangles(i, 4);
    test_image(y:y+h-1, x:x+w-1) = 0.8;
end

% Add some circular features (simulating buttons or indicators)
circles = [
    [60, 250, 15];   % y, x, radius
    [140, 250, 12];
    [180, 50, 10];
];

for i = 1:size(circles, 1)
    cy = circles(i, 1);
    cx = circles(i, 2);
    r = circles(i, 3);
    
    [X, Y] = meshgrid(1:img_size(2), 1:img_size(1));
    circle_mask = (X - cx).^2 + (Y - cy).^2 <= r^2;
    test_image(circle_mask) = 0.6;
end

% Add some fine details (simulating text)
for i = 1:5
    y_start = 30 + i * 25;
    for j = 1:8
        x_start = 20 + j * 30;
        % Small rectangles simulating text characters
        if x_start + 8 <= img_size(2) && y_start + 12 <= img_size(1)
            test_image(y_start:y_start+8, x_start:x_start+6) = 0.4;
        end
    end
end

% Smooth the image slightly to avoid aliasing
test_image = imgaussfilt(test_image, 0.5);

% Save test image
imwrite(test_image, 'ui_images/test_arrow.png');

fprintf('Sample UI test image saved to ui_images/test_arrow.png\n');

%% Display summary
fprintf('\n=== SAMPLE DATA GENERATION COMPLETE ===\n');
fprintf('Created files:\n');
fprintf('  - %d BSF data files in bsf_data/ directory\n', num_kernels);
fprintf('  - 1 test UI image in ui_images/ directory\n');
fprintf('\nYou can now run the main_workflow.m script to test the system.\n');
fprintf('Note: The grid coordinates in main_workflow.m are set to example values.\n');
fprintf('For real applications, replace them with actual simulation coordinates.\n');
