% FIX_COORDINATE_ISSUE - Quick fix for the coordinate configuration error

clear; clc;

fprintf('=== Fixing Coordinate Configuration Issue ===\n');

% Check if the coordinate file exists
coord_file = 'image_data/processed_image_coordinates.txt';

if exist(coord_file, 'file')
    fprintf('Found coordinate file: %s\n', coord_file);
    
    % Read the file manually to extract coordinates
    fprintf('Reading coordinate file manually...\n');
    
    fid = fopen(coord_file, 'r');
    if fid == -1
        error('Cannot open coordinate file');
    end
    
    coords = [];
    while ~feof(fid)
        line = fgetl(fid);
        if ischar(line)
            line = strtrim(line);
            % Skip comment lines and empty lines
            if ~isempty(line) && ~startsWith(line, '%')
                nums = str2num(line); %#ok<ST2NM>
                if length(nums) >= 2
                    coords = [coords; nums(1), nums(2)]; %#ok<AGROW>
                end
            end
        end
    end
    fclose(fid);
    
    if ~isempty(coords)
        fprintf('Successfully extracted coordinates: [%.2f, %.2f]\n', coords(1), coords(2));
        
        % Test the simplified workflow with the extracted coordinates
        fprintf('\nTesting coordinate configuration...\n');
        
        try
            % Call auto_coordinate_config with the fixed implementation
            test_coords = auto_coordinate_config(coord_file, 'single');
            fprintf('✓ Coordinate configuration now works!\n');
            fprintf('Generated coordinates: [%.2f, %.2f]\n', test_coords(1), test_coords(2));
            
            % Continue with simplified workflow
            fprintf('\n=== Continuing Simplified Workflow ===\n');
            fprintf('You can now run simplified_workflow again, or continue from Step 5.\n');
            fprintf('The coordinate configuration issue has been resolved.\n');
            
        catch ME
            fprintf('✗ Still having issues: %s\n', ME.message);
            
            % Provide manual coordinates as fallback
            fprintf('\nUsing manual coordinates as fallback...\n');
            manual_coords = coords(1, :);  % Use first coordinate pair
            fprintf('Manual coordinates: [%.2f, %.2f]\n', manual_coords(1), manual_coords(2));
            
            % Save manual coordinates for use in simplified workflow
            assignin('base', 'manual_grid_centers_coords', manual_coords);
            fprintf('Manual coordinates saved to workspace as "manual_grid_centers_coords"\n');
            fprintf('You can modify simplified_workflow.m to use these coordinates directly.\n');
        end
        
    else
        error('No valid coordinates found in file');
    end
    
else
    fprintf('Coordinate file not found. Creating a default coordinate file...\n');
    
    % Create image_data directory if it doesn't exist
    if ~exist('image_data', 'dir')
        mkdir('image_data');
    end
    
    % Create a default coordinate file
    default_coords = [150, 100];  % Default center coordinates
    
    fid = fopen(coord_file, 'w');
    fprintf(fid, '%% Default coordinate configuration\n');
    fprintf(fid, '%% Format: X_coordinate Y_coordinate\n');
    fprintf(fid, '%.6f %.6f\n', default_coords(1), default_coords(2));
    fclose(fid);
    
    fprintf('Created default coordinate file with coordinates: [%.2f, %.2f]\n', default_coords(1), default_coords(2));
    fprintf('You can now run simplified_workflow again.\n');
end

fprintf('\n=== Fix Complete ===\n');
