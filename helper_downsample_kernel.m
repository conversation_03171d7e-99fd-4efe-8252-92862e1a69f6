function low_res_kernel = helper_downsample_kernel(high_res_kernel, scale_factor)
    % HELPER_DOWNSAMPLE_KERNEL - Energy-preserving kernel downsampling
    %
    % Converts a high-resolution kernel to a low-resolution one using
    % energy-preserving binning (summation). This ensures that the total
    % energy of the kernel is preserved during downsampling.
    %
    % Inputs:
    %   high_res_kernel - High-resolution kernel (2D numeric matrix)
    %   scale_factor - Downsampling factor (positive integer)
    %
    % Output:
    %   low_res_kernel - Downsampled kernel with preserved energy

    % Input validation
    if ~isnumeric(high_res_kernel) || ndims(high_res_kernel) ~= 2
        error('high_res_kernel must be a 2D numeric matrix.');
    end

    if ~isnumeric(scale_factor) || ~isscalar(scale_factor) || scale_factor <= 0 || mod(scale_factor, 1) ~= 0
        error('scale_factor must be a positive integer.');
    end

    if any(isnan(high_res_kernel(:))) || any(isinf(high_res_kernel(:)))
        error('high_res_kernel contains NaN or Inf values.');
    end

    [hr_h, hr_w] = size(high_res_kernel);

    if hr_h < scale_factor || hr_w < scale_factor
        error('Kernel dimensions (%dx%d) must be at least as large as scale_factor (%d).', ...
            hr_h, hr_w, scale_factor);
    end

    % Store original energy for validation
    original_energy = sum(high_res_kernel(:));

    % Pad with zeros if size is not perfectly divisible by scale_factor
    pad_h = mod(hr_h, scale_factor);
    pad_w = mod(hr_w, scale_factor);
    if pad_h ~= 0, pad_h = scale_factor - pad_h; end
    if pad_w ~= 0, pad_w = scale_factor - pad_w; end

    if pad_h > 0 || pad_w > 0
        try
            high_res_kernel = padarray(high_res_kernel, [pad_h, pad_w], 0, 'post');
        catch ME
            error('Failed to pad kernel: %s', ME.message);
        end
    end

    [hr_h, hr_w] = size(high_res_kernel);
    lr_h = hr_h / scale_factor;
    lr_w = hr_w / scale_factor;

    % Validate that dimensions are now compatible
    if mod(hr_h, scale_factor) ~= 0 || mod(hr_w, scale_factor) ~= 0
        error('Internal error: Padded dimensions are not divisible by scale_factor.');
    end

    try
        % Energy-preserving binning using vectorized operations
        % Step 1: Reshape and sum along height dimension
        reshaped_h = reshape(high_res_kernel, scale_factor, lr_h, hr_w);
        sum_h = squeeze(sum(reshaped_h, 1));

        % Step 2: Reshape and sum along width dimension
        reshaped_w = reshape(sum_h', scale_factor, lr_w, lr_h);
        sum_w = squeeze(sum(reshaped_w, 1));

        % Step 3: Transpose to get final result
        low_res_kernel = sum_w';

    catch ME
        error('Failed during binning operation: %s', ME.message);
    end

    % Validate result dimensions
    if any(size(low_res_kernel) ~= [lr_h, lr_w])
        error('Internal error: Output kernel has incorrect dimensions.');
    end

    % Energy conservation check
    downsampled_energy = sum(low_res_kernel(:));
    energy_ratio = abs(downsampled_energy - original_energy) / max(abs(original_energy), eps);

    if energy_ratio > 1e-10
        warning('Energy conservation error: %.2e%% energy change during downsampling.', ...
            energy_ratio * 100);
    end

    % Normalize the kernel to preserve image brightness after convolution
    kernel_sum = sum(low_res_kernel(:));
    if abs(kernel_sum) > 1e-12
        low_res_kernel = low_res_kernel / kernel_sum;
    else
        warning('Kernel has near-zero sum. Using uniform distribution.');
        low_res_kernel = ones(lr_h, lr_w) / (lr_h * lr_w);
    end

    % Final validation
    if any(isnan(low_res_kernel(:))) || any(isinf(low_res_kernel(:)))
        error('Output kernel contains NaN or Inf values.');
    end
end
