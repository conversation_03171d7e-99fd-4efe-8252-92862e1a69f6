function image_processing_workflow(mode, image_path, varargin)
    % IMAGE_PROCESSING_WORKFLOW - Comprehensive image processing for HUD pre-compensation
    %
    % This function provides multiple modes for converting images to formats
    % compatible with the HUD pre-compensation workflow.
    %
    % Inputs:
    %   mode - Processing mode:
    %          'bsf' - Convert to single BSF file format
    %          'speos' - Convert to large SPEOS export format
    %          'demo' - Run demonstration with default image
    %   image_path - Path to input image (optional for demo mode)
    %   varargin - Additional parameters depending on mode
    %
    % Usage Examples:
    %   % Demo mode (uses default image)
    %   image_processing_workflow('demo')
    %
    %   % Convert to BSF format
    %   image_processing_workflow('bsf', 'sim_images/postsim_image.png')
    %   image_processing_workflow('bsf', 'sim_images/postsim_image.png', 'bsf_data', 'bsf_0')
    %
    %   % Convert to SPEOS format
    %   image_processing_workflow('speos', 'sim_images/postsim_image.png')
    %   image_processing_workflow('speos', 'sim_images/postsim_image.png', 'my_speos_data.txt')
    %
    % Based on the provided example code:
    %   image = imread("/path/to/image.png");
    %   rgbimage = image(:,:,1:3);
    %   grayimage = rgb2gray(rgbimage);
    %   figure; imagesc(grayimage); colormap("gray"); colorbar;

    if nargin < 1
        mode = 'demo';
    end

    fprintf('=== HUD Image Processing Workflow ===\n');
    fprintf('Mode: %s\n', mode);

    switch lower(mode)
        case 'demo'
            run_demo_workflow();
            
        case 'bsf'
            if nargin < 2
                error('Image path is required for BSF mode');
            end
            
            % Parse additional arguments
            output_folder = 'bsf_data';
            bsf_name = 'bsf_0';
            if nargin >= 3, output_folder = varargin{1}; end
            if nargin >= 4, bsf_name = varargin{2}; end
            
            % Convert to BSF format
            image_to_bsf_converter(image_path, output_folder, bsf_name);
            
            % Suggest next steps
            fprintf('\n=== Next Steps for BSF Mode ===\n');
            fprintf('1. Run the main workflow: main_workflow\n');
            fprintf('2. Or set data_source = ''%s'' in main_workflow.m\n', output_folder);
            
        case 'speos'
            if nargin < 2
                error('Image path is required for SPEOS mode');
            end
            
            % Parse additional arguments
            output_file = 'large_speos_sample.txt';
            grid_dims = [6, 6];
            scale_factor = 100;
            if nargin >= 3, output_file = varargin{1}; end
            if nargin >= 4, grid_dims = varargin{2}; end
            if nargin >= 5, scale_factor = varargin{3}; end
            
            % Convert to SPEOS format
            convert_image_to_speos_format(image_path, output_file, grid_dims, scale_factor);
            
            % Suggest next steps
            fprintf('\n=== Next Steps for SPEOS Mode ===\n');
            fprintf('1. The workflow will automatically detect the large SPEOS file\n');
            fprintf('2. Run the main workflow: main_workflow\n');
            fprintf('3. Or manually set data_source = ''%s'' in main_workflow.m\n', output_file);
            
        otherwise
            error('Unknown mode: %s. Valid modes are: demo, bsf, speos', mode);
    end
end

function run_demo_workflow()
    % Run demonstration workflow with example image processing
    
    fprintf('\n=== Demo Mode: Image Processing Example ===\n');
    
    % Find a demo image
    demo_images = {
        'sim_images/postsim_image.png',
        'sim_images/5x5_spots_distortion-square-speos-1x.png',
        'sim_images/5x5_spots_distortion-square.png',
        'ui_images/test_arrow.png'
    };
    
    demo_image = '';
    for i = 1:length(demo_images)
        if exist(demo_images{i}, 'file')
            demo_image = demo_images{i};
            break;
        end
    end
    
    if isempty(demo_image)
        fprintf('No demo image found. Creating a synthetic image...\n');
        create_synthetic_demo_image();
        demo_image = 'demo_synthetic_image.png';
    end
    
    fprintf('Using demo image: %s\n', demo_image);
    
    % Follow the provided example code exactly
    fprintf('\nStep 1: Reading image...\n');
    image = imread(demo_image);
    fprintf('Image size: %dx%dx%d\n', size(image, 1), size(image, 2), size(image, 3));
    
    fprintf('\nStep 2: Extracting RGB channels...\n');
    if size(image, 3) >= 3
        rgbimage = image(:,:,1:3);  % Following the example: rgbimage = image(:,:,1:3);
    else
        rgbimage = repmat(image, [1, 1, 3]);
    end
    fprintf('RGB image size: %dx%dx%d\n', size(rgbimage, 1), size(rgbimage, 2), size(rgbimage, 3));
    
    fprintf('\nStep 3: Converting to grayscale...\n');
    grayimage = rgb2gray(rgbimage);  % Following the example: grayimage = rgb2gray(rgbimage);
    fprintf('Grayscale image size: %dx%d\n', size(grayimage, 1), size(grayimage, 2));
    
    fprintf('\nStep 4: Displaying results...\n');
    % Following the example: figure; imagesc(grayimage); colormap("gray"); colorbar;
    figure('Name', 'Demo: Image Processing Results');
    
    subplot(2, 2, 1);
    if size(image, 3) >= 3
        imshow(image);
        title('Original Image');
    else
        imagesc(image);
        colormap('gray');
        title('Original Image');
    end
    
    subplot(2, 2, 2);
    imshow(rgbimage);
    title('RGB Image');
    
    subplot(2, 2, 3);
    imagesc(grayimage);
    colormap('gray');
    colorbar;
    title('Grayscale Image');
    xlabel('X coordinate');
    ylabel('Y coordinate');
    
    subplot(2, 2, 4);
    histogram(grayimage(:), 50);
    title('Intensity Histogram');
    xlabel('Intensity Value');
    ylabel('Frequency');
    
    % Display statistics
    fprintf('\n=== Image Statistics ===\n');
    fprintf('Min intensity: %d\n', min(grayimage(:)));
    fprintf('Max intensity: %d\n', max(grayimage(:)));
    fprintf('Mean intensity: %.2f\n', mean(grayimage(:)));
    fprintf('Standard deviation: %.2f\n', std(double(grayimage(:))));
    
    fprintf('\n=== Demo Completed ===\n');
    fprintf('The demo shows the basic image processing steps:\n');
    fprintf('1. Read image: imread()\n');
    fprintf('2. Extract RGB: image(:,:,1:3)\n');
    fprintf('3. Convert to grayscale: rgb2gray()\n');
    fprintf('4. Display: imagesc() with colormap("gray") and colorbar\n');
    fprintf('\nTo process your own images:\n');
    fprintf('- BSF mode: image_processing_workflow(''bsf'', ''your_image.png'')\n');
    fprintf('- SPEOS mode: image_processing_workflow(''speos'', ''your_image.png'')\n');
end

function create_synthetic_demo_image()
    % Create a synthetic demo image if no real images are available
    
    fprintf('Creating synthetic demo image...\n');
    
    % Create a simple test pattern
    img_size = [200, 300, 3];
    demo_img = zeros(img_size, 'uint8');
    
    % Add some patterns
    [X, Y] = meshgrid(1:img_size(2), 1:img_size(1));
    
    % Create a gradient pattern
    demo_img(:,:,1) = uint8(255 * X / img_size(2));  % Red channel
    demo_img(:,:,2) = uint8(255 * Y / img_size(1));  % Green channel
    demo_img(:,:,3) = uint8(128 * ones(img_size(1), img_size(2)));  % Blue channel
    
    % Add some geometric shapes
    center_x = img_size(2) / 2;
    center_y = img_size(1) / 2;
    radius = 50;
    
    circle_mask = (X - center_x).^2 + (Y - center_y).^2 < radius^2;
    demo_img(circle_mask) = 255;  % White circle
    
    % Save the synthetic image
    imwrite(demo_img, 'demo_synthetic_image.png');
    fprintf('Synthetic demo image saved as: demo_synthetic_image.png\n');
end
