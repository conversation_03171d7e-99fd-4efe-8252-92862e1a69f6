function [output_file, coord_file] = image_to_text_converter(image_path, output_folder, base_name)
    % IMAGE_TO_TEXT_CONVERTER - Convert image to text format for HUD processing
    %
    % This function implements the simplified single-image processing workflow.
    % It reads an image, converts it to grayscale using the provided example code,
    % and saves it as text format that can be processed by the existing workflow.
    %
    % Inputs:
    %   image_path - Path to the input image file
    %   output_folder - Output folder for text data (default: 'image_data')
    %   base_name - Base name for output files (default: 'converted_image')
    %
    % Outputs:
    %   output_file - Path to the generated text data file
    %   coord_file - Path to the generated coordinate file
    %
    % Usage:
    %   image_to_text_converter('sim_images/postsim_image.png')
    %   [data_file, coord_file] = image_to_text_converter('sim_images/postsim_image.png', 'image_data', 'my_image')
    %
    % Based on the provided example code:
    %   image = imread("/path/to/image.png");
    %   rgbimage = image(:,:,1:3);
    %   grayimage = rgb2gray(rgbimage);
    %   figure; imagesc(grayimage); colormap("gray"); colorbar;

    % Set default parameters
    if nargin < 1
        error('Image path is required.');
    end
    
    if nargin < 2 || isempty(output_folder)
        output_folder = 'image_data';
    end
    
    if nargin < 3 || isempty(base_name)
        base_name = 'converted_image';
    end

    fprintf('=== Image to Text Format Conversion ===\n');
    fprintf('Converting image: %s\n', image_path);

    % Check if input image exists
    if ~exist(image_path, 'file')
        error('Image file "%s" does not exist.', image_path);
    end

    % Create output folder if it doesn't exist
    if ~exist(output_folder, 'dir')
        mkdir(output_folder);
        fprintf('Created output folder: %s\n', output_folder);
    end

    try
        % Step 1: Read image (following the provided example)
        fprintf('Step 1: Reading image...\n');
        image = imread(image_path);
        
        % Display image information
        fprintf('Image size: %dx%dx%d\n', size(image, 1), size(image, 2), size(image, 3));
        fprintf('Image class: %s\n', class(image));

        % Step 2: Extract RGB channels (following example: rgbimage = image(:,:,1:3))
        fprintf('Step 2: Extracting RGB channels...\n');
        if size(image, 3) >= 3
            rgbimage = image(:,:,1:3);  % Following the corrected variable name
            fprintf('Extracted RGB channels\n');
        elseif size(image, 3) == 1
            % Grayscale image - convert to RGB for consistency
            rgbimage = repmat(image, [1, 1, 3]);
            fprintf('Converted grayscale to RGB format\n');
        else
            error('Unsupported image format with %d channels', size(image, 3));
        end

        % Step 3: Convert RGB to grayscale (following example: grayimage = rgb2gray(rgbimage))
        fprintf('Step 3: Converting to grayscale...\n');
        grayimage = rgb2gray(rgbimage);  % Following the corrected variable name
        fprintf('Grayscale conversion completed\n');

        % Step 4: Display results (following example: figure; imagesc(grayimage); colormap("gray"); colorbar;)
        fprintf('Step 4: Displaying results...\n');
        figure('Name', sprintf('Converted Image - %s', base_name));
        imagesc(grayimage);  % Following the corrected variable name
        colormap('gray');
        colorbar;
        title(sprintf('Grayscale Image from %s', image_path));
        xlabel('X coordinate');
        ylabel('Y coordinate');

        % Step 5: Convert to text format for processing
        fprintf('Step 5: Converting to text format...\n');
        
        % Convert to double and normalize
        intensity_data = double(grayimage) / 255.0;
        
        % Get image dimensions
        [img_height, img_width] = size(intensity_data);
        
        % Create coordinate grids (X, Y, Intensity format)
        [X, Y] = meshgrid(1:img_width, 1:img_height);
        
        % Flatten the arrays
        X_flat = X(:);
        Y_flat = Y(:);
        I_flat = intensity_data(:);
        
        % Filter out very low intensity values to reduce file size
        intensity_threshold = 0.001;
        valid_indices = I_flat > intensity_threshold;
        
        X_filtered = X_flat(valid_indices);
        Y_filtered = Y_flat(valid_indices);
        I_filtered = I_flat(valid_indices);
        
        fprintf('Filtered data points: %d (from %d total pixels)\n', ...
            length(X_filtered), length(X_flat));
        
        % Step 6: Save as text file (space-separated format)
        output_file = fullfile(output_folder, [base_name '_data.txt']);
        fprintf('Step 6: Saving text data to: %s\n', output_file);
        
        fid = fopen(output_file, 'w');
        if fid == -1
            error('Cannot create output file: %s', output_file);
        end
        
        % Write header
        fprintf(fid, '%% Image data converted from: %s\n', image_path);
        fprintf(fid, '%% Format: X_coordinate Y_coordinate Intensity\n');
        fprintf(fid, '%% Image size: %dx%d pixels\n', img_height, img_width);
        fprintf(fid, '%% Total data points: %d\n', length(X_filtered));
        
        % Write data in space-separated format
        for i = 1:length(X_filtered)
            fprintf(fid, '%.6f %.6f %.6f\n', X_filtered(i), Y_filtered(i), I_filtered(i));
        end
        
        fclose(fid);
        
        % Step 7: Generate automatic coordinate configuration
        fprintf('Step 7: Generating automatic coordinate configuration...\n');
        coord_file = generate_auto_coordinates(intensity_data, output_folder, base_name);
        
        % Display statistics
        fprintf('\n=== Conversion Statistics ===\n');
        fprintf('Input image: %s\n', image_path);
        fprintf('Output data file: %s\n', output_file);
        fprintf('Output coordinate file: %s\n', coord_file);
        fprintf('Image dimensions: %dx%d pixels\n', img_height, img_width);
        fprintf('Data points written: %d\n', length(X_filtered));
        fprintf('Intensity range: [%.6f, %.6f]\n', min(I_filtered), max(I_filtered));
        
        % Save visualization
        vis_file = fullfile(output_folder, [base_name '_visualization.png']);
        saveas(gcf, vis_file);
        fprintf('Visualization saved: %s\n', vis_file);

        fprintf('\n=== Conversion completed successfully! ===\n');
        fprintf('Next steps:\n');
        fprintf('1. Use the generated files in the simplified workflow\n');
        fprintf('2. The coordinate system has been automatically configured\n');
        fprintf('3. Run the main workflow with the new single-image processing mode\n');

    catch ME
        fprintf('ERROR during conversion: %s\n', ME.message);
        rethrow(ME);
    end
end

function coord_file = generate_auto_coordinates(intensity_data, output_folder, base_name)
    % Generate automatic coordinate configuration based on image centroid
    
    fprintf('  - Computing image centroid for coordinate configuration...\n');
    
    % Calculate image centroid (center of mass)
    [img_height, img_width] = size(intensity_data);
    [X, Y] = meshgrid(1:img_width, 1:img_height);
    
    % Calculate weighted centroid
    total_intensity = sum(intensity_data(:));
    if total_intensity > eps
        centroid_x = sum(sum(X .* intensity_data)) / total_intensity;
        centroid_y = sum(sum(Y .* intensity_data)) / total_intensity;
    else
        % Fallback to geometric center
        centroid_x = img_width / 2;
        centroid_y = img_height / 2;
    end
    
    fprintf('  - Image centroid: (%.2f, %.2f)\n', centroid_x, centroid_y);
    
    % For simplified single-image processing, use the centroid as the single coordinate
    % This replaces the need for 36 grid coordinates
    grid_centers_coords = [centroid_x, centroid_y];
    
    % Save coordinate configuration
    coord_file = fullfile(output_folder, [base_name '_coordinates.txt']);
    fid = fopen(coord_file, 'w');
    if fid == -1
        error('Cannot create coordinate file: %s', coord_file);
    end
    
    fprintf(fid, '%% Automatic coordinate configuration for single-image processing\n');
    fprintf(fid, '%% Generated from image centroid calculation\n');
    fprintf(fid, '%% Format: X_coordinate Y_coordinate\n');
    fprintf(fid, '%% Image dimensions: %dx%d\n', img_height, img_width);
    fprintf(fid, '%.6f %.6f\n', grid_centers_coords(1), grid_centers_coords(2));
    
    fclose(fid);
    
    fprintf('  - Coordinate configuration saved: %s\n', coord_file);
end
