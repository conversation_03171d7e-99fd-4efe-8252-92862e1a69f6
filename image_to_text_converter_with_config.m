function [output_file, coord_file] = image_to_text_converter_with_config(image_path, output_folder, base_name, cfg)
    % IMAGE_TO_TEXT_CONVERTER_WITH_CONFIG - Config-aware image to text converter
    %
    % This function extends the original image_to_text_converter with unified
    % configuration system support, ensuring consistent parameter usage.
    %
    % Inputs:
    %   image_path - Path to the input image file
    %   output_folder - Output folder for text data
    %   base_name - Base name for output files
    %   cfg - Configuration structure from config.m
    %
    % Outputs:
    %   output_file - Path to the generated text data file
    %   coord_file - Path to the generated coordinate file

    fprintf('=== Image to Text Conversion (Config-Aware) ===\n');
    fprintf('Converting image: %s\n', image_path);

    % Check if input image exists
    if ~exist(image_path, 'file')
        error('Image file "%s" does not exist.', image_path);
    end

    % Create output folder if it doesn't exist
    if ~exist(output_folder, 'dir')
        mkdir(output_folder);
        fprintf('Created output folder: %s\n', output_folder);
    end

    try
        % Step 1: Read image (following the provided example)
        fprintf('Step 1: Reading image...\n');
        image = imread(image_path);
        
        % Display image information
        fprintf('Image size: %dx%dx%d\n', size(image, 1), size(image, 2), size(image, 3));
        fprintf('Image class: %s\n', class(image));

        % Step 2: Extract RGB channels (following example: rgbimage = image(:,:,1:3))
        fprintf('Step 2: Extracting RGB channels...\n');
        if size(image, 3) >= 3
            rgbimage = image(:,:,1:3);  % Following the corrected variable name
            fprintf('Extracted RGB channels\n');
        elseif size(image, 3) == 1
            % Grayscale image - convert to RGB for consistency
            rgbimage = repmat(image, [1, 1, 3]);
            fprintf('Converted grayscale to RGB format\n');
        else
            error('Unsupported image format with %d channels', size(image, 3));
        end

        % Step 3: Convert RGB to grayscale (following example: grayimage = rgb2gray(rgbimage))
        fprintf('Step 3: Converting to grayscale...\n');
        grayimage = rgb2gray(rgbimage);  % Following the corrected variable name
        fprintf('Grayscale conversion completed\n');

        % Step 4: Display results (following example: figure; imagesc(grayimage); colormap("gray"); colorbar;)
        if cfg.enable_progress_display
            fprintf('Step 4: Displaying results...\n');
            figure('Name', sprintf('Converted Image - %s (Config)', base_name));
            imagesc(grayimage);  % Following the corrected variable name
            colormap('gray');
            colorbar;
            title(sprintf('Grayscale Image from %s', image_path));
            xlabel('X coordinate');
            ylabel('Y coordinate');
        end

        % Step 5: Convert to text format for processing
        fprintf('Step 5: Converting to text format...\n');
        
        % Convert to double and normalize
        intensity_data = double(grayimage) / 255.0;
        
        % Get image dimensions
        [img_height, img_width] = size(intensity_data);
        
        % Create coordinate grids (X, Y, Intensity format)
        [X, Y] = meshgrid(1:img_width, 1:img_height);
        
        % Flatten the arrays
        X_flat = X(:);
        Y_flat = Y(:);
        I_flat = intensity_data(:);
        
        % Filter out very low intensity values using config threshold
        intensity_threshold = cfg.intensity_threshold;
        valid_indices = I_flat > intensity_threshold;
        
        X_filtered = X_flat(valid_indices);
        Y_filtered = Y_flat(valid_indices);
        I_filtered = I_flat(valid_indices);
        
        fprintf('Filtered data points: %d (from %d total pixels)\n', ...
            length(X_filtered), length(X_flat));
        fprintf('Using intensity threshold: %.6f (from config)\n', intensity_threshold);
        
        % Step 6: Save as text file (space-separated format)
        output_file = fullfile(output_folder, [base_name '_data.txt']);
        fprintf('Step 6: Saving text data to: %s\n', output_file);
        
        fid = fopen(output_file, 'w');
        if fid == -1
            error('Cannot create output file: %s', output_file);
        end
        
        % Write header with config information
        fprintf(fid, '%% Image data converted from: %s\n', image_path);
        fprintf(fid, '%% Format: X_coordinate Y_coordinate Intensity\n');
        fprintf(fid, '%% Image size: %dx%d pixels\n', img_height, img_width);
        fprintf(fid, '%% Total data points: %d\n', length(X_filtered));
        fprintf(fid, '%% Intensity threshold: %.6f (from config)\n', intensity_threshold);
        fprintf(fid, '%% Configuration timestamp: %s\n', cfg.validation_timestamp);
        
        % Write data in space-separated format
        for i = 1:length(X_filtered)
            fprintf(fid, '%.6f %.6f %.6f\n', X_filtered(i), Y_filtered(i), I_filtered(i));
        end
        
        fclose(fid);
        
        % Step 7: Generate automatic coordinate configuration with config support
        fprintf('Step 7: Generating automatic coordinate configuration...\n');
        coord_file = generate_auto_coordinates_with_config(intensity_data, output_folder, base_name, cfg);
        
        % Display statistics
        fprintf('\n=== Conversion Statistics (Config-Aware) ===\n');
        fprintf('Input image: %s\n', image_path);
        fprintf('Output data file: %s\n', output_file);
        fprintf('Output coordinate file: %s\n', coord_file);
        fprintf('Image dimensions: %dx%d pixels\n', img_height, img_width);
        fprintf('Data points written: %d\n', length(X_filtered));
        fprintf('Intensity range: [%.6f, %.6f]\n', min(I_filtered), max(I_filtered));
        fprintf('Config intensity threshold: %.6f\n', intensity_threshold);
        
        % Save visualization if enabled
        if cfg.enable_progress_display
            vis_file = fullfile(output_folder, [base_name '_visualization.png']);
            saveas(gcf, vis_file);
            fprintf('Visualization saved: %s\n', vis_file);
        end

        fprintf('\n=== Conversion completed successfully! ===\n');

    catch ME
        fprintf('ERROR during conversion: %s\n', ME.message);
        rethrow(ME);
    end
end

function coord_file = generate_auto_coordinates_with_config(intensity_data, output_folder, base_name, cfg)
    % Generate automatic coordinate configuration with config support
    
    fprintf('  - Computing image centroid for coordinate configuration...\n');
    
    % Calculate image centroid (center of mass)
    [img_height, img_width] = size(intensity_data);
    [X, Y] = meshgrid(1:img_width, 1:img_height);
    
    % Calculate weighted centroid
    total_intensity = sum(intensity_data(:));
    if total_intensity > eps
        centroid_x = sum(sum(X .* intensity_data)) / total_intensity;
        centroid_y = sum(sum(Y .* intensity_data)) / total_intensity;
    else
        % Fallback to geometric center
        centroid_x = img_width / 2;
        centroid_y = img_height / 2;
    end
    
    fprintf('  - Image centroid: (%.2f, %.2f)\n', centroid_x, centroid_y);
    
    % For simplified single-image processing, use the centroid as the single coordinate
    % This replaces the need for multiple grid coordinates
    grid_centers_coords = [centroid_x, centroid_y];
    
    % Save coordinate configuration with config metadata
    coord_file = fullfile(output_folder, [base_name '_coordinates.txt']);
    fid = fopen(coord_file, 'w');
    if fid == -1
        error('Cannot create coordinate file: %s', coord_file);
    end
    
    fprintf(fid, '%% Automatic coordinate configuration for single-image processing\n');
    fprintf(fid, '%% Generated from image centroid calculation\n');
    fprintf(fid, '%% Format: X_coordinate Y_coordinate\n');
    fprintf(fid, '%% Image dimensions: %dx%d\n', img_height, img_width);
    fprintf(fid, '%% Configuration timestamp: %s\n', cfg.validation_timestamp);
    fprintf(fid, '%% Fallback coordinates available: [%.2f, %.2f]\n', ...
        cfg.simplified_workflow.fallback_coordinates(1), cfg.simplified_workflow.fallback_coordinates(2));
    fprintf(fid, '%.6f %.6f\n', grid_centers_coords(1), grid_centers_coords(2));
    
    fclose(fid);
    
    fprintf('  - Coordinate configuration saved: %s\n', coord_file);
end
