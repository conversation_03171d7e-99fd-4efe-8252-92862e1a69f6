function main_workflow_with_config(cfg)
    % MAIN_WORKFLOW_WITH_CONFIG - Main workflow using unified configuration
    %
    % This function runs the main HUD pre-compensation workflow using
    % parameters from the unified configuration system.
    %
    % Usage:
    %   main_workflow_with_config()                    % Use default config
    %   main_workflow_with_config(config('research'))  % Use research preset
    %   main_workflow_with_config(config('fast'))      % Use fast preset
    %
    % The function maintains full backward compatibility with the original
    % main_workflow.m while providing centralized parameter management.

    % Load configuration if not provided
    if nargin < 1
        cfg = config('default');
    end

    clear; clc; close all;

    %% --- 1. CONFIGURATION FROM UNIFIED SYSTEM ---
    fprintf('=== HUD Pre-Compensation Workflow (Unified Config) ===\n');
    fprintf('Step 1: Loading configuration parameters...\n');

    % Extract parameters from config structure
    grid_dims = cfg.grid_dims;
    oversampling_rate = cfg.oversampling_rate;
    matrix_size = cfg.matrix_size;
    noise_ratio_step1 = cfg.noise_ratio_step1;
    noise_ratio_step2 = cfg.noise_ratio_step2;

    % File paths from config
    bsf_data_folder = cfg.paths.bsf_data_dir;
    ui_image_path = fullfile(cfg.paths.ui_images_dir, cfg.files.default_ui_image);
    output_map_file = cfg.files.kernel_map_output;
    output_image_file = cfg.files.precompensated_output;

    % Image conversion settings
    convert_image_to_bsf = cfg.main_workflow.enable_image_conversion;
    if convert_image_to_bsf
        image_conversion_path = fullfile(cfg.paths.sim_images_dir, 'postsim_image.png');
        converted_bsf_name = 'bsf_0';
    end

    fprintf('  - Configuration loaded: %dx%d grid, %dx%d matrix, %dx oversampling\n', ...
        grid_dims(1), grid_dims(2), matrix_size, matrix_size, oversampling_rate);

    %% --- 2. DATA SOURCE AUTO-DETECTION ---
    fprintf('\nStep 2: Auto-detecting data source...\n');

    if cfg.main_workflow.auto_detect_data_source
        % Auto-detect available data source
        if exist('large_speos_sample.txt', 'file')
            data_source = 'large_speos_sample.txt';
            fprintf('  - Detected large SPEOS file. Will use enhanced processing mode.\n');
        elseif exist(bsf_data_folder, 'dir')
            data_source = bsf_data_folder;
            fprintf('  - Using standard .mat file format from directory.\n');
        else
            data_source = bsf_data_folder;  % Default fallback
            fprintf('  - Using default data source (will create sample data if needed).\n');
        end
    else
        data_source = bsf_data_folder;
        fprintf('  - Using configured data source: %s\n', data_source);
    end

    %% --- 3. COORDINATE CONFIGURATION ---
    fprintf('\nStep 3: Configuring coordinate system...\n');

    % Generate grid coordinates based on configuration
    num_points = prod(grid_dims);
    if num_points == 36
        % Standard 6x6 grid - use example coordinates
        fprintf('  - Using standard 6x6 grid coordinates\n');
        grid_centers_highres_coords = [
            % Example coordinates for 6x6 grid (replace with actual data)
            100, 100; 200, 100; 300, 100; 400, 100; 500, 100; 600, 100;
            100, 200; 200, 200; 300, 200; 400, 200; 500, 200; 600, 200;
            100, 300; 200, 300; 300, 300; 400, 300; 500, 300; 600, 300;
            100, 400; 200, 400; 300, 400; 400, 400; 500, 400; 600, 400;
            100, 500; 200, 500; 300, 500; 400, 500; 500, 500; 600, 500;
            100, 600; 200, 600; 300, 600; 400, 600; 500, 600; 600, 600
        ];
    else
        % Generate coordinates for custom grid
        fprintf('  - Generating coordinates for %dx%d grid\n', grid_dims(1), grid_dims(2));
        [grid_x, grid_y] = meshgrid(1:grid_dims(2), 1:grid_dims(1));
        grid_centers_highres_coords = [grid_x(:) * 100, grid_y(:) * 100];
    end

    fprintf('  - Generated %d coordinate points\n', size(grid_centers_highres_coords, 1));

    %% --- 4. IMAGE CONVERSION (OPTIONAL) ---
    if convert_image_to_bsf
        fprintf('\nStep 4: Converting image to BSF format...\n');
        try
            image_to_bsf_converter(image_conversion_path, bsf_data_folder, converted_bsf_name);
            fprintf('  - Image conversion completed successfully.\n');
        catch ME
            fprintf('  - ERROR in image conversion: %s\n', ME.message);
            fprintf('  - Continuing with existing BSF data...\n');
        end
    end

    %% --- 5. OFFLINE STAGE ---
    fprintf('\nStep 5: Starting offline stage...\n');

    % Check if pre-computed kernel map exists
    if ~exist(output_map_file, 'file')
        fprintf('  - Kernel map not found. Starting offline computation...\n');

        % Validate data source exists
        if ~exist(data_source, 'file') && ~exist(data_source, 'dir')
            error('Data source "%s" does not exist. Please check the path and ensure data files are present.', data_source);
        end

        try
            % Run offline stage with config parameters
            offline_stage_auto(data_source, output_map_file, ...
                grid_dims, grid_centers_highres_coords, oversampling_rate, matrix_size, ...
                noise_ratio_step1, noise_ratio_step2);

            fprintf('  - Offline stage completed successfully.\n');

        catch ME
            fprintf('  - ERROR in offline stage: %s\n', ME.message);
            error('Offline stage failed. Please check your data files and configuration.');
        end
    else
        fprintf('  - Pre-computed kernel map found: %s\n', output_map_file);
    end

    %% --- 6. ONLINE STAGE ---
    fprintf('\nStep 6: Starting online stage...\n');

    % Load pre-computed kernel map
    try
        fprintf('  - Loading kernel map...\n');
        map_data = load(output_map_file);
        
        prekernel_map_lowres = map_data.prekernel_map_lowres;
        grid_centers_lowres_coords = map_data.grid_centers_lowres_coords;
        
        fprintf('  - Kernel map loaded successfully\n');

    catch ME
        error('Failed to load kernel map: %s', ME.message);
    end

    % Load and validate UI image
    try
        fprintf('  - Loading UI image: %s\n', ui_image_path);
        
        if ~exist(ui_image_path, 'file')
            error('UI image file "%s" does not exist.', ui_image_path);
        end
        
        ui_image = imread(ui_image_path);
        ui_image = im2double(ui_image);
        
        % Convert to grayscale if needed
        if size(ui_image, 3) > 1
            ui_image = rgb2gray(ui_image);
        end
        
        fprintf('  - UI image loaded: %dx%d pixels\n', size(ui_image, 1), size(ui_image, 2));

    catch ME
        error('Failed to load UI image: %s', ME.message);
    end

    % Apply pre-compensation
    try
        fprintf('  - Applying pre-compensation...\n');
        
        precompensated_image = online_stage_auto(ui_image, ...
            prekernel_map_lowres, grid_centers_lowres_coords);
        
        fprintf('  - Pre-compensation completed successfully\n');

    catch ME
        error('Failed to apply pre-compensation: %s', ME.message);
    end

    %% --- 7. SAVE AND DISPLAY RESULTS ---
    fprintf('\nStep 7: Saving and displaying results...\n');

    % Save pre-compensated image
    try
        imwrite(precompensated_image, output_image_file);
        fprintf('  - Pre-compensated image saved: %s\n', output_image_file);
    catch ME
        warning('Failed to save output image: %s', ME.message);
    end

    % Display results
    try
        figure('Name', 'HUD Pre-Compensation Results (Unified Config)', 'Position', [100, 100, 1200, 500]);
        
        % Original image
        subplot(1, 2, 1);
        imshow(ui_image);
        title('Original UI Image', 'FontSize', 14, 'FontWeight', 'bold');
        
        % Pre-compensated image
        subplot(1, 2, 2);
        imshow(precompensated_image);
        title('Pre-Compensated Image', 'FontSize', 14, 'FontWeight', 'bold');
        
        sgtitle('HUD Optical Pre-Compensation Results', 'FontSize', 16, 'FontWeight', 'bold');
        
        % Display processing summary
        fprintf('\n=== PROCESSING SUMMARY ===\n');
        fprintf('Configuration: %s\n', cfg.validation_timestamp);
        fprintf('Input image size: %dx%d pixels\n', size(ui_image, 1), size(ui_image, 2));
        fprintf('Grid dimensions: %dx%d\n', grid_dims(1), grid_dims(2));
        fprintf('Oversampling rate: %dx\n', oversampling_rate);
        fprintf('Matrix size: %dx%d\n', matrix_size, matrix_size);
        fprintf('Noise ratios: %.1e (BSF->PSF), %.1e (PSF->PreKernel)\n', noise_ratio_step1, noise_ratio_step2);
        fprintf('Output files:\n');
        fprintf('  - Kernel map: %s\n', output_map_file);
        fprintf('  - Pre-compensated image: %s\n', output_image_file);

    catch ME
        warning('Failed to display results: %s', ME.message);
    end

    fprintf('\n=== WORKFLOW COMPLETED SUCCESSFULLY ===\n');
end
