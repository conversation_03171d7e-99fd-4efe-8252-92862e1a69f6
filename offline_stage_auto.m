function offline_stage_auto(simulation_file, map_file, grid_dims, ...
    grid_centers_highres_coords, oversampling_rate, matrix_size, noise_r1, noise_r2)
    % OFFLINE_STAGE_AUTO - Generate pre-compensation kernel map from simulation data
    %
    % This function implements the offline calibration stage that converts BSF
    % (Beam Spread Function) data from optical simulation into a pre-compensation
    % kernel map for real-time image processing.
    %
    % Inputs:
    %   simulation_file - Path to simulation data file
    %   map_file - Output file for pre-compensation kernel map
    %   grid_dims - Dimensions of sampling grid [rows, cols]
    %   grid_centers_highres_coords - High-res coordinates of grid centers
    %   oversampling_rate - Ratio of simulation to UI image resolution
    %   matrix_size - Size of BSF extraction matrix
    %   noise_r1 - Noise ratio for BSF->PSF deconvolution
    %   noise_r2 - Noise ratio for PSF->PreKernel deconvolution

    fprintf('  - Starting offline stage processing...\n');

    % Input validation
    if ~exist(simulation_file, 'file') && ~exist(simulation_file, 'dir')
        error('Simulation file "%s" does not exist.', simulation_file);
    end

    if any(grid_dims <= 0) || length(grid_dims) ~= 2
        error('grid_dims must be a 2-element vector with positive values.');
    end

    if oversampling_rate <= 0
        error('oversampling_rate must be positive.');
    end

    if matrix_size <= 0 || mod(matrix_size, 1) ~= 0
        error('matrix_size must be a positive integer.');
    end

    expected_num_points = prod(grid_dims);
    if size(grid_centers_highres_coords, 1) ~= expected_num_points
        error('Expected %d coordinate points for %dx%d grid, got %d.', ...
            expected_num_points, grid_dims(1), grid_dims(2), ...
            size(grid_centers_highres_coords, 1));
    end

    % --- STEP 1: PARSE RAW DATA TO GET BSFs AND COORDINATES ---
    try
        [bsf_cells, precise_coords_highres] = helper_parse_speos_data(...
            simulation_file, grid_centers_highres_coords, matrix_size);
    catch ME
        error('Failed to parse simulation data: %s', ME.message);
    end

    num_kernels = prod(grid_dims);
    prekernel_map_lowres = cell(grid_dims);

    % --- STEP 2: DEFINE SOURCE AND TARGET FUNCTIONS ---
    fprintf('  - Defining source and target functions...\n');

    % Create rectangular source function (5x5 pixels as per documentation)
    source_size = 5;
    rect_source = ones(source_size, source_size) / (source_size^2);

    % Create delta target function (ideal point source)
    delta_target = zeros(matrix_size, matrix_size);
    center_idx = ceil(matrix_size / 2);
    delta_target(center_idx, center_idx) = 1;

    % --- STEP 3: PROCESS EACH BSF TO GENERATE PRE-KERNELS ---
    fprintf('  - Processing %d BSF kernels...\n', num_kernels);

    for i = 1:num_kernels
        fprintf('    Processing kernel %d/%d...\n', i, num_kernels);

        % Get normalized BSF
        BSF_from_speos = bsf_cells{i};
        BSF_from_speos = BSF_from_speos / sum(BSF_from_speos(:));

        % Validate BSF data
        if any(isnan(BSF_from_speos(:))) || any(isinf(BSF_from_speos(:)))
            warning('BSF %d contains NaN or Inf values. Replacing with zeros.', i);
            BSF_from_speos(isnan(BSF_from_speos) | isinf(BSF_from_speos)) = 0;
            BSF_from_speos = BSF_from_speos / max(sum(BSF_from_speos(:)), eps);
        end

        % Step 3a: BSF -> PSF conversion using Wiener deconvolution
        PSF_estimated = deconvolve_wiener(BSF_from_speos, rect_source, noise_r1);

        % Step 3b: PSF -> Pre-Kernel conversion using Wiener deconvolution
        pre_kernel_high_res = deconvolve_wiener(PSF_estimated, delta_target, noise_r2);

        % Validate pre-kernel
        if any(isnan(pre_kernel_high_res(:))) || any(isinf(pre_kernel_high_res(:)))
            warning('Pre-kernel %d contains NaN or Inf values. Using identity kernel.', i);
            pre_kernel_high_res = delta_target;
        end

        % Step 3c: Downsample to low resolution
        low_res_kernel = helper_downsample_kernel(pre_kernel_high_res, oversampling_rate);

        % Store in grid layout
        [row, col] = ind2sub(grid_dims, i);
        prekernel_map_lowres{row, col} = low_res_kernel;
    end

    % --- STEP 4: COMPUTE CORRESPONDING LOW-RESOLUTION COORDINATES ---
    fprintf('  - Computing low-resolution grid coordinates...\n');

    % Scale coordinates down by oversampling rate
    grid_centers_lowres_coords = precise_coords_highres / oversampling_rate;

    % --- STEP 5: SAVE FINAL KERNEL MAP ---
    fprintf('  - Saving pre-compensation kernel map...\n');

    try
        save(map_file, 'prekernel_map_lowres', 'grid_centers_lowres_coords', 'grid_dims');
        fprintf('  - Pre-compensation kernel map saved to "%s".\n', map_file);
    catch ME
        error('Failed to save kernel map: %s', ME.message);
    end

    fprintf('  - Offline stage completed successfully.\n');
end

function result = deconvolve_wiener(observed, kernel, noise_ratio)
    % DECONVOLVE_WIENER - Perform Wiener deconvolution
    %
    % This function implements Wiener deconvolution to estimate the original
    % signal from an observed blurred signal and the blurring kernel.
    %
    % Inputs:
    %   observed - The observed (blurred) signal
    %   kernel - The blurring kernel (PSF or source function)
    %   noise_ratio - Regularization parameter for noise suppression
    %
    % Output:
    %   result - Deconvolved signal estimate
    %
    % Algorithm:
    %   H_est = (G* · S) / (|G|² + noise_ratio · |S|²)
    %   where G is the kernel, S is the observed signal, and * denotes conjugate

    % Ensure inputs are the same size by padding the smaller one
    [obs_h, obs_w] = size(observed);
    [ker_h, ker_w] = size(kernel);

    % Determine the size for FFT (should be at least the sum of both sizes)
    fft_h = obs_h + ker_h - 1;
    fft_w = obs_w + ker_w - 1;

    % Make dimensions even for better FFT performance
    fft_h = fft_h + mod(fft_h, 2);
    fft_w = fft_w + mod(fft_w, 2);

    % Pad both signals to the same size
    observed_padded = padarray(observed, [fft_h - obs_h, fft_w - obs_w], 0, 'post');
    kernel_padded = padarray(kernel, [fft_h - ker_h, fft_w - ker_w], 0, 'post');

    % Compute FFTs
    G = fft2(kernel_padded);  % Kernel in frequency domain
    S = fft2(observed_padded); % Observed signal in frequency domain

    % Wiener deconvolution in frequency domain
    % H = (G* · S) / (|G|² + noise_ratio · |S|²)
    G_conj = conj(G);
    G_mag_sq = abs(G).^2;
    S_mag_sq = abs(S).^2;

    % Avoid division by zero
    denominator = G_mag_sq + noise_ratio * S_mag_sq;
    denominator(denominator < eps) = eps;

    H = (G_conj .* S) ./ denominator;

    % Convert back to spatial domain
    result_full = real(ifft2(H));

    % Extract the central portion (same size as original observed signal)
    start_h = floor((fft_h - obs_h) / 2) + 1;
    start_w = floor((fft_w - obs_w) / 2) + 1;
    result = result_full(start_h:start_h+obs_h-1, start_w:start_w+obs_w-1);

    % Ensure non-negative values (physical constraint)
    result = max(result, 0);

    % Normalize to preserve energy
    result_sum = sum(result(:));
    if result_sum > eps
        result = result / result_sum;
    end
end
