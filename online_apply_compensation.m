function output_image = online_stage_auto(input_image, ...
    prekernel_map_lowres, grid_centers_lowres_coords)
    % ONLINE_STAGE_AUTO - Apply real-time pre-compensation to input image
    %
    % This function applies spatially-varying pre-compensation to an input image
    % using pre-computed kernel maps. The compensation varies smoothly across
    % the image based on interpolation between discrete kernel locations.
    %
    % Inputs:
    %   input_image - Input UI image (2D grayscale, values in [0,1])
    %   prekernel_map_lowres - Cell array of pre-compensation kernels
    %   grid_centers_lowres_coords - Coordinates of kernel centers in image space
    %
    % Output:
    %   output_image - Pre-compensated image ready for HUD display

    % Input validation
    if ~isnumeric(input_image) || ndims(input_image) ~= 2
        error('Input image must be a 2D numeric matrix.');
    end

    if any(input_image(:) < 0) || any(input_image(:) > 1)
        warning('Input image values should be in range [0,1]. Clamping values.');
        input_image = max(0, min(1, input_image));
    end

    if ~iscell(prekernel_map_lowres)
        error('prekernel_map_lowres must be a cell array.');
    end

    if ~isnumeric(grid_centers_lowres_coords) || size(grid_centers_lowres_coords, 2) ~= 2
        error('grid_centers_lowres_coords must be a numeric matrix with 2 columns.');
    end

    [img_h, img_w] = size(input_image);
    num_kernels = numel(prekernel_map_lowres);

    if size(grid_centers_lowres_coords, 1) ~= num_kernels
        error('Number of coordinates (%d) must match number of kernels (%d).', ...
            size(grid_centers_lowres_coords, 1), num_kernels);
    end

    if num_kernels < 3
        error('At least 3 kernels are required for interpolation.');
    end

    % Validate kernels
    for i = 1:num_kernels
        if ~isnumeric(prekernel_map_lowres{i}) || ndims(prekernel_map_lowres{i}) ~= 2
            error('Kernel %d must be a 2D numeric matrix.', i);
        end

        if any(isnan(prekernel_map_lowres{i}(:))) || any(isinf(prekernel_map_lowres{i}(:)))
            error('Kernel %d contains NaN or Inf values.', i);
        end
    end

    % Validate coordinates
    if any(isnan(grid_centers_lowres_coords(:))) || any(isinf(grid_centers_lowres_coords(:)))
        error('Grid coordinates contain NaN or Inf values.');
    end

    % --- Step 1: Generate candidate images ---
    fprintf('    Generating %d candidate images...\n', num_kernels);

    candidate_images = zeros(img_h, img_w, num_kernels);

    % Use parallel processing if available, otherwise fall back to regular loop
    try
        % Check if Parallel Computing Toolbox is available
        if license('test', 'Distrib_Computing_Toolbox') && ~isempty(gcp('nocreate'))
            use_parallel = true;
        else
            use_parallel = false;
        end
    catch
        use_parallel = false;
    end

    if use_parallel
        try
            parfor i = 1:num_kernels
                candidate_images(:,:,i) = imfilter(input_image, prekernel_map_lowres{i}, ...
                    'replicate', 'conv', 'same');
            end
        catch ME
            warning('Parallel processing failed: %s. Falling back to sequential processing.', ME.message);
            use_parallel = false;
        end
    end

    if ~use_parallel
        for i = 1:num_kernels
            candidate_images(:,:,i) = imfilter(input_image, prekernel_map_lowres{i}, ...
                'replicate', 'conv', 'same');
        end
    end

    % --- Step 2: Spatial interpolation ---
    fprintf('    Performing spatial interpolation...\n');

    try
        % Create scattered interpolant for kernel indices
        % This maps image coordinates to kernel indices
        F = scatteredInterpolant(grid_centers_lowres_coords(:,1), ...
                                 grid_centers_lowres_coords(:,2), ...
                                 (1:num_kernels)', 'linear', 'none');

        % Create query grid for all image pixels
        [Xq, Yq] = meshgrid(1:img_w, 1:img_h);

        % Find the interpolated kernel index for each pixel
        image_indices = F(Xq, Yq);

    catch ME
        error('Failed to create spatial interpolant: %s', ME.message);
    end

    % Handle interpolation between candidate images
    try
        % Get floor and ceiling indices for interpolation
        idx1 = floor(image_indices);
        idx2 = ceil(image_indices);

        % Calculate interpolation weights
        weight = image_indices - idx1;

        % Handle pixels outside the convex hull of grid points
        outside_mask = isnan(image_indices);
        num_outside = sum(outside_mask(:));
        if num_outside > 0
            fprintf('    Warning: %d pixels (%.1f%%) are outside kernel coverage area.\n', ...
                num_outside, 100 * num_outside / numel(image_indices));
        end

        % Clamp indices to valid range and handle NaN values
        weight(outside_mask) = 0;
        idx1(outside_mask | idx1 < 1) = 1;
        idx2(outside_mask | idx2 > num_kernels) = num_kernels;
        idx1(idx1 > num_kernels) = num_kernels;
        idx2(idx2 < 1) = 1;

        % Perform vectorized interpolation
        V1 = candidate_images(sub2ind(size(candidate_images), Yq, Xq, idx1));
        V2 = candidate_images(sub2ind(size(candidate_images), Yq, Xq, idx2));

        % Linear interpolation between the two nearest candidate images
        output_image = (1 - weight) .* V1 + weight .* V2;

        % For pixels outside the kernel coverage, use the original image
        output_image(outside_mask) = input_image(outside_mask);

    catch ME
        error('Failed during spatial interpolation: %s', ME.message);
    end

    % Final validation and cleanup
    output_image = real(output_image);  % Ensure real values
    output_image = max(0, min(1, output_image));  % Clamp to valid range

    % Check for any remaining invalid values
    if any(isnan(output_image(:))) || any(isinf(output_image(:)))
        warning('Output image contains NaN or Inf values. Replacing with input image values.');
        invalid_mask = isnan(output_image) | isinf(output_image);
        output_image(invalid_mask) = input_image(invalid_mask);
    end

    fprintf('    Pre-compensation completed successfully.\n');
end
