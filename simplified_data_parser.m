function [bsf_cells, precise_coords] = simplified_data_parser(data_source, grid_centers_coords, matrix_size)
    % SIMPLIFIED_DATA_PARSER - Parse single image data for simplified workflow
    %
    % This function replaces the complex multi-BSF processing with a simplified
    % single-image processing approach. It reads text format data generated by
    % the image conversion function and creates a single BSF for processing.
    %
    % Inputs:
    %   data_source - Path to text data file (X, Y, Intensity format)
    %   grid_centers_coords - Single coordinate point [X, Y] (1×2 matrix)
    %   matrix_size - Size of the output BSF matrix (typically 31)
    %
    % Outputs:
    %   bsf_cells - Cell array containing single normalized BSF matrix
    %   precise_coords - Refined center coordinate [X, Y] (1×2 matrix)
    %
    % This function supports the simplified workflow that processes a single
    % image instead of multiple BSF files, making the system more user-friendly.

    fprintf('=== Simplified Data Parser ===\n');
    fprintf('Processing single image data source: %s\n', data_source);

    % Input validation
    if ~exist(data_source, 'file')
        error('Data source "%s" does not exist.', data_source);
    end

    if size(grid_centers_coords, 2) ~= 2
        error('grid_centers_coords must have 2 columns (X, Y coordinates).');
    end

    if matrix_size <= 0 || mod(matrix_size, 1) ~= 0
        error('matrix_size must be a positive integer.');
    end

    % For simplified processing, we expect only one coordinate point
    if size(grid_centers_coords, 1) ~= 1
        fprintf('Warning: Multiple coordinates provided, using the first one for simplified processing.\n');
        grid_centers_coords = grid_centers_coords(1, :);
    end

    try
        % Read the text data file
        fprintf('Reading text data file...\n');
        data = readmatrix(data_source, 'CommentStyle', '%');
        
        if size(data, 2) < 3
            error('Data file must have at least 3 columns (X, Y, Intensity)');
        end
        
        X = data(:, 1);
        Y = data(:, 2);
        I = data(:, 3);
        
        fprintf('Loaded %d data points\n', length(X));
        fprintf('X range: [%.2f, %.2f]\n', min(X), max(X));
        fprintf('Y range: [%.2f, %.2f]\n', min(Y), max(Y));
        fprintf('Intensity range: [%.6f, %.6f]\n', min(I), max(I));
        
        % Create BSF matrix by interpolating data onto regular grid
        fprintf('Creating BSF matrix...\n');
        bsf_matrix = create_bsf_matrix(X, Y, I, matrix_size);
        
        % Calculate precise centroid
        fprintf('Calculating precise coordinates...\n');
        precise_coord = calculate_precise_centroid(bsf_matrix);
        
        % Normalize BSF to unit sum (probability distribution)
        bsf_sum = sum(bsf_matrix(:));
        if bsf_sum > eps
            bsf_matrix = bsf_matrix / bsf_sum;
        else
            warning('BSF matrix has zero or near-zero sum. Using uniform distribution.');
            bsf_matrix = ones(matrix_size, matrix_size) / (matrix_size^2);
        end
        
        % Store results (single BSF for simplified processing)
        bsf_cells = {bsf_matrix};
        precise_coords = precise_coord;
        
        % Display results
        fprintf('BSF matrix statistics:\n');
        fprintf('  Size: %dx%d\n', size(bsf_matrix, 1), size(bsf_matrix, 2));
        fprintf('  Sum: %.6f\n', sum(bsf_matrix(:)));
        fprintf('  Min: %.6f, Max: %.6f\n', min(bsf_matrix(:)), max(bsf_matrix(:)));
        fprintf('  Precise centroid: (%.2f, %.2f)\n', precise_coord(1), precise_coord(2));
        
        fprintf('=== Simplified parsing completed successfully ===\n');

    catch ME
        fprintf('ERROR in simplified data parsing: %s\n', ME.message);
        rethrow(ME);
    end
end

function bsf_matrix = create_bsf_matrix(X, Y, I, matrix_size)
    % Create BSF matrix by interpolating scattered data onto regular grid
    
    % Define regular grid for BSF matrix
    x_min = min(X); x_max = max(X);
    y_min = min(Y); y_max = max(Y);
    
    % Create regular grid
    x_grid = linspace(x_min, x_max, matrix_size);
    y_grid = linspace(y_min, y_max, matrix_size);
    [Xq, Yq] = meshgrid(x_grid, y_grid);
    
    % Interpolate scattered data onto regular grid
    try
        % Use griddata for interpolation
        bsf_matrix = griddata(X, Y, I, Xq, Yq, 'linear', 0);
        
        % Handle NaN values
        bsf_matrix(isnan(bsf_matrix)) = 0;
        
        % Ensure non-negative values
        bsf_matrix = max(bsf_matrix, 0);
        
    catch ME
        warning('Interpolation failed: %s. Using binning method.', ME.message);
        
        % Fallback: use binning method
        bsf_matrix = zeros(matrix_size, matrix_size);
        
        % Bin the data
        x_edges = linspace(x_min, x_max, matrix_size + 1);
        y_edges = linspace(y_min, y_max, matrix_size + 1);
        
        for i = 1:length(X)
            % Find bin indices
            x_idx = find(X(i) >= x_edges(1:end-1) & X(i) < x_edges(2:end), 1);
            y_idx = find(Y(i) >= y_edges(1:end-1) & Y(i) < y_edges(2:end), 1);
            
            if ~isempty(x_idx) && ~isempty(y_idx)
                bsf_matrix(y_idx, x_idx) = bsf_matrix(y_idx, x_idx) + I(i);
            end
        end
    end
    
    % Smooth the matrix slightly to reduce noise
    if exist('imgaussfilt', 'file')
        bsf_matrix = imgaussfilt(bsf_matrix, 0.5);
    end
end

function precise_coord = calculate_precise_centroid(bsf_matrix)
    % Calculate precise centroid of BSF matrix
    
    [matrix_height, matrix_width] = size(bsf_matrix);
    [X, Y] = meshgrid(1:matrix_width, 1:matrix_height);
    
    % Calculate weighted centroid
    total_intensity = sum(bsf_matrix(:));
    if total_intensity > eps
        centroid_x = sum(sum(X .* bsf_matrix)) / total_intensity;
        centroid_y = sum(sum(Y .* bsf_matrix)) / total_intensity;
    else
        % Fallback to geometric center
        centroid_x = matrix_width / 2;
        centroid_y = matrix_height / 2;
    end
    
    precise_coord = [centroid_x, centroid_y];
end
