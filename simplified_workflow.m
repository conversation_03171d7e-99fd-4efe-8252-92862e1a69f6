% =========================================================================
% SIMPLIFIED HUD PRE-COMPENSATION WORKFLOW
% =========================================================================
% This script implements a simplified workflow for HUD optical pre-compensation
% using single image processing instead of multiple BSF files. It automatically
% handles image conversion, coordinate configuration, and pre-compensation.
%
% Key Features:
% - Single image input (replaces 36 BSF files)
% - Automatic coordinate system configuration
% - Text-based data format processing
% - Simplified user interface
%
% Usage:
%   1. Place your image in the sim_images folder
%   2. Run this script
%   3. The system will automatically process the image and generate results
%
% Author: HUD Pre-Compensation System (Simplified Version)
% Date: 2025
% =========================================================================

clear; clc; close all;

%% --- 1. SIMPLIFIED CONFIGURATION ---
fprintf('=== Simplified HUD Pre-Compensation Workflow ===\n');
fprintf('Step 1: Configuring simplified workflow parameters...\n');

% -- Single Image Input Configuration --
% Specify the input image path
input_image_path = 'sim_images/postsim_image.png';  % Change this to your image

% Alternative images to try if the primary one doesn't exist
alternative_images = {
    'sim_images/5x5_spots_distortion-square-speos-1x.png',
    'sim_images/5x5_spots_distortion-square.png',
    'ui_images/test_arrow.png'
};

% -- Output Configuration --
output_folder = 'image_data';  % Folder for converted image data
base_name = 'processed_image';  % Base name for output files
output_map_file = 'simplified_prekernel_map.mat';  % Simplified kernel map
output_image_file = 'simplified_precompensated_image.png';  % Output image

% -- Processing Parameters --
matrix_size = 31;             % BSF matrix size (keep consistent with original)
oversampling_rate = 3;        % Oversampling rate for downsampling
noise_ratio_step1 = 1e-5;     % Regularization for BSF -> PSF conversion
noise_ratio_step2 = 1e-3;     % Regularization for PSF -> PreKernel conversion

% -- UI Image for Testing --
ui_image_path = 'ui_images/test_arrow.png';

fprintf('  - Input image: %s\n', input_image_path);
fprintf('  - Output folder: %s\n', output_folder);
fprintf('  - Matrix size: %dx%d\n', matrix_size, matrix_size);

%% --- 2. IMAGE VALIDATION AND SELECTION ---
fprintf('\nStep 2: Validating input image...\n');

% Check if primary image exists
if ~exist(input_image_path, 'file')
    fprintf('  - Primary image not found: %s\n', input_image_path);
    fprintf('  - Searching for alternative images...\n');
    
    found_image = false;
    for i = 1:length(alternative_images)
        if exist(alternative_images{i}, 'file')
            input_image_path = alternative_images{i};
            fprintf('  - Using alternative image: %s\n', input_image_path);
            found_image = true;
            break;
        end
    end
    
    if ~found_image
        error('No suitable input image found. Please check the image paths.');
    end
else
    fprintf('  - Using primary image: %s\n', input_image_path);
end

%% --- 3. IMAGE CONVERSION TO TEXT FORMAT ---
fprintf('\nStep 3: Converting image to text format...\n');

try
    % Convert image to text format using the provided example code flow
    [data_file, coord_file] = image_to_text_converter(input_image_path, output_folder, base_name);
    fprintf('  - Image conversion completed successfully\n');
    fprintf('  - Data file: %s\n', data_file);
    fprintf('  - Coordinate file: %s\n', coord_file);
    
catch ME
    fprintf('  - ERROR in image conversion: %s\n', ME.message);
    error('Failed to convert image. Please check the input image.');
end

%% --- 4. AUTOMATIC COORDINATE CONFIGURATION ---
fprintf('\nStep 4: Configuring coordinate system automatically...\n');

try
    % Use automatic coordinate configuration
    grid_centers_coords = auto_coordinate_config(coord_file, 'single');
    fprintf('  - Automatic coordinate configuration completed\n');
    fprintf('  - Generated %d coordinate point(s)\n', size(grid_centers_coords, 1));
    
catch ME
    fprintf('  - ERROR in coordinate configuration: %s\n', ME.message);
    error('Failed to configure coordinates automatically.');
end

%% --- 5. SIMPLIFIED OFFLINE STAGE ---
fprintf('\nStep 5: Starting simplified offline stage...\n');

% Check if pre-computed kernel map exists
if ~exist(output_map_file, 'file')
    fprintf('  - Kernel map not found. Starting offline computation...\n');

    try
        % Use simplified data parser instead of complex multi-BSF processing
        [bsf_cells, precise_coords] = simplified_data_parser(data_file, grid_centers_coords, matrix_size);
        
        % Create simplified offline processing
        fprintf('  - Processing single BSF for pre-compensation...\n');
        
        % Define source and target functions (same as original)
        source_size = 5;
        rect_source = ones(source_size, source_size) / (source_size^2);
        
        delta_target = zeros(matrix_size, matrix_size);
        center_idx = ceil(matrix_size / 2);
        delta_target(center_idx, center_idx) = 1;
        
        % Process the single BSF
        BSF_from_image = bsf_cells{1};
        
        % Step 1: BSF -> PSF conversion using Wiener deconvolution
        PSF_estimated = deconvolve_wiener(BSF_from_image, rect_source, noise_ratio_step1);
        
        % Step 2: PSF -> Pre-Kernel conversion using Wiener deconvolution
        pre_kernel_high_res = deconvolve_wiener(PSF_estimated, delta_target, noise_ratio_step2);
        
        % Validate pre-kernel
        if any(isnan(pre_kernel_high_res(:))) || any(isinf(pre_kernel_high_res(:)))
            warning('Pre-kernel contains NaN or Inf values. Using identity kernel.');
            pre_kernel_high_res = delta_target;
        end
        
        % Step 3: Downsample to low resolution
        low_res_kernel = helper_downsample_kernel(pre_kernel_high_res, oversampling_rate);
        
        % Create simplified kernel map (single kernel instead of grid)
        prekernel_map_lowres = {low_res_kernel};
        grid_centers_lowres_coords = precise_coords / oversampling_rate;
        grid_dims = [1, 1];  % Single kernel
        
        % Save simplified kernel map
        save(output_map_file, 'prekernel_map_lowres', 'grid_centers_lowres_coords', 'grid_dims');
        fprintf('  - Simplified kernel map saved to: %s\n', output_map_file);
        
        fprintf('  - Simplified offline stage completed successfully\n');

    catch ME
        fprintf('  - ERROR in simplified offline stage: %s\n', ME.message);
        rethrow(ME);
    end
else
    fprintf('  - Pre-computed kernel map found: %s\n', output_map_file);
end

%% --- 6. SIMPLIFIED ONLINE STAGE ---
fprintf('\nStep 6: Starting simplified online stage...\n');

% Load pre-computed kernel map
try
    fprintf('  - Loading simplified kernel map...\n');
    map_data = load(output_map_file);
    
    prekernel_map_lowres = map_data.prekernel_map_lowres;
    grid_centers_lowres_coords = map_data.grid_centers_lowres_coords;
    
    fprintf('  - Kernel map loaded successfully\n');

catch ME
    error('Failed to load kernel map: %s', ME.message);
end

% Load and validate UI image
try
    fprintf('  - Loading UI image: %s\n', ui_image_path);
    
    if ~exist(ui_image_path, 'file')
        error('UI image file "%s" does not exist.', ui_image_path);
    end
    
    ui_image = imread(ui_image_path);
    ui_image = im2double(ui_image);
    
    % Convert to grayscale if needed
    if size(ui_image, 3) > 1
        ui_image = rgb2gray(ui_image);
    end
    
    fprintf('  - UI image loaded: %dx%d pixels\n', size(ui_image, 1), size(ui_image, 2));

catch ME
    error('Failed to load UI image: %s', ME.message);
end

% Apply simplified pre-compensation
try
    fprintf('  - Applying simplified pre-compensation...\n');
    
    % For single kernel, apply direct convolution
    precompensated_image = imfilter(ui_image, prekernel_map_lowres{1}, 'replicate', 'conv', 'same');
    
    fprintf('  - Pre-compensation completed successfully\n');

catch ME
    error('Failed to apply pre-compensation: %s', ME.message);
end

%% --- 7. SAVE AND DISPLAY RESULTS ---
fprintf('\nStep 7: Saving and displaying results...\n');

% Save pre-compensated image
try
    imwrite(precompensated_image, output_image_file);
    fprintf('  - Pre-compensated image saved: %s\n', output_image_file);
catch ME
    warning('Failed to save output image: %s', ME.message);
end

% Display results
try
    figure('Name', 'Simplified HUD Pre-Compensation Results', 'Position', [100, 100, 1200, 500]);
    
    % Original image
    subplot(1, 2, 1);
    imshow(ui_image);
    title('Original UI Image', 'FontSize', 14, 'FontWeight', 'bold');
    
    % Pre-compensated image
    subplot(1, 2, 2);
    imshow(precompensated_image);
    title('Pre-Compensated Image (Simplified)', 'FontSize', 14, 'FontWeight', 'bold');
    
    sgtitle('Simplified HUD Optical Pre-Compensation Results', 'FontSize', 16, 'FontWeight', 'bold');
    
    % Display processing summary
    fprintf('\n=== SIMPLIFIED PROCESSING SUMMARY ===\n');
    fprintf('Input image: %s\n', input_image_path);
    fprintf('UI image: %s\n', ui_image_path);
    fprintf('Processing mode: Single image (simplified)\n');
    fprintf('Matrix size: %dx%d\n', matrix_size, matrix_size);
    fprintf('Oversampling rate: %dx\n', oversampling_rate);
    fprintf('Output files:\n');
    fprintf('  - Data file: %s\n', data_file);
    fprintf('  - Coordinate file: %s\n', coord_file);
    fprintf('  - Kernel map: %s\n', output_map_file);
    fprintf('  - Pre-compensated image: %s\n', output_image_file);

catch ME
    warning('Failed to display results: %s', ME.message);
end

fprintf('\n=== SIMPLIFIED WORKFLOW COMPLETED SUCCESSFULLY ===\n');
fprintf('The simplified workflow has processed your single image and generated\n');
fprintf('pre-compensation results without requiring multiple BSF files.\n');

function result = deconvolve_wiener(observed, kernel, noise_ratio)
    % DECONVOLVE_WIENER - Perform Wiener deconvolution
    % (Same implementation as in the original workflow)

    % Ensure inputs are the same size by padding the smaller one
    [obs_h, obs_w] = size(observed);
    [ker_h, ker_w] = size(kernel);

    % Determine the size for FFT (should be at least the sum of both sizes)
    fft_h = obs_h + ker_h - 1;
    fft_w = obs_w + ker_w - 1;

    % Make dimensions even for better FFT performance
    fft_h = fft_h + mod(fft_h, 2);
    fft_w = fft_w + mod(fft_w, 2);

    % Pad both signals to the same size
    observed_padded = padarray(observed, [fft_h - obs_h, fft_w - obs_w], 0, 'post');
    kernel_padded = padarray(kernel, [fft_h - ker_h, fft_w - ker_w], 0, 'post');

    % Compute FFTs
    G = fft2(kernel_padded);  % Kernel in frequency domain
    S = fft2(observed_padded); % Observed signal in frequency domain

    % Wiener deconvolution in frequency domain
    G_conj = conj(G);
    G_mag_sq = abs(G).^2;
    S_mag_sq = abs(S).^2;

    % Avoid division by zero
    denominator = G_mag_sq + noise_ratio * S_mag_sq;
    denominator(denominator < eps) = eps;

    H = (G_conj .* S) ./ denominator;

    % Convert back to spatial domain
    result_full = real(ifft2(H));

    % Extract the central portion (same size as original observed signal)
    start_h = floor((fft_h - obs_h) / 2) + 1;
    start_w = floor((fft_w - obs_w) / 2) + 1;
    result = result_full(start_h:start_h+obs_h-1, start_w:start_w+obs_w-1);

    % Ensure non-negative values (physical constraint)
    result = max(result, 0);

    % Normalize to preserve energy
    result_sum = sum(result(:));
    if result_sum > eps
        result = result / result_sum;
    end
end
