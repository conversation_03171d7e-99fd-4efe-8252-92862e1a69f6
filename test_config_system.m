% TEST_CONFIG_SYSTEM - Test the unified configuration system
%
% This script validates the unified configuration system and demonstrates
% its usage with different presets and custom parameters.

clear; clc; close all;

fprintf('=== Testing Unified Configuration System ===\n');

%% Test 1: Default Configuration
fprintf('\n--- Test 1: Default Configuration ---\n');
try
    cfg_default = config();
    fprintf('✓ Default configuration loaded successfully\n');
    fprintf('  Grid: %dx%d, Matrix: %dx%d, Oversampling: %dx\n', ...
        cfg_default.grid_dims(1), cfg_default.grid_dims(2), ...
        cfg_default.matrix_size, cfg_default.matrix_size, ...
        cfg_default.oversampling_rate);
catch ME
    fprintf('✗ Default configuration failed: %s\n', ME.message);
end

%% Test 2: Preset Configurations
fprintf('\n--- Test 2: Preset Configurations ---\n');

presets = {'fast', 'research', 'simple'};
for i = 1:length(presets)
    try
        cfg = config(presets{i});
        fprintf('✓ %s preset loaded successfully\n', presets{i});
        fprintf('  Grid: %dx%d, Matrix: %dx%d, Oversampling: %dx\n', ...
            cfg.grid_dims(1), cfg.grid_dims(2), ...
            cfg.matrix_size, cfg.matrix_size, ...
            cfg.oversampling_rate);
    catch ME
        fprintf('✗ %s preset failed: %s\n', presets{i}, ME.message);
    end
end

%% Test 3: Custom Configuration
fprintf('\n--- Test 3: Custom Configuration ---\n');
try
    custom_params = struct();
    custom_params.grid_dims = [4, 4];
    custom_params.matrix_size = 41;
    custom_params.oversampling_rate = 2;
    custom_params.noise_ratio_step1 = 1e-6;
    
    cfg_custom = config('custom', custom_params);
    fprintf('✓ Custom configuration loaded successfully\n');
    fprintf('  Grid: %dx%d, Matrix: %dx%d, Oversampling: %dx\n', ...
        cfg_custom.grid_dims(1), cfg_custom.grid_dims(2), ...
        cfg_custom.matrix_size, cfg_custom.matrix_size, ...
        cfg_custom.oversampling_rate);
    fprintf('  Noise ratio 1: %.1e\n', cfg_custom.noise_ratio_step1);
catch ME
    fprintf('✗ Custom configuration failed: %s\n', ME.message);
end

%% Test 4: Parameter Validation
fprintf('\n--- Test 4: Parameter Validation ---\n');
try
    % Test invalid parameters
    invalid_params = struct();
    invalid_params.grid_dims = [-1, 0];  % Invalid
    invalid_params.matrix_size = 30;     % Even number (should be corrected)
    invalid_params.oversampling_rate = -1; % Invalid
    
    cfg_invalid = config('custom', invalid_params);
    fprintf('✓ Parameter validation working correctly\n');
    fprintf('  Corrected grid: %dx%d\n', cfg_invalid.grid_dims(1), cfg_invalid.grid_dims(2));
    fprintf('  Corrected matrix size: %d\n', cfg_invalid.matrix_size);
    fprintf('  Corrected oversampling: %d\n', cfg_invalid.oversampling_rate);
catch ME
    fprintf('✗ Parameter validation failed: %s\n', ME.message);
end

%% Test 5: Configuration Comparison
fprintf('\n--- Test 5: Configuration Comparison ---\n');

% Compare key parameters across presets
presets_to_compare = {'default', 'fast', 'research', 'simple'};
fprintf('Preset Comparison:\n');
fprintf('%-10s %-8s %-8s %-12s %-12s %-12s\n', 'Preset', 'Grid', 'Matrix', 'Oversample', 'Noise1', 'Noise2');
fprintf('%-10s %-8s %-8s %-12s %-12s %-12s\n', '------', '----', '------', '---------', '------', '------');

for i = 1:length(presets_to_compare)
    try
        cfg = config(presets_to_compare{i});
        fprintf('%-10s %dx%-3d %-8d %-12d %-12.1e %-12.1e\n', ...
            presets_to_compare{i}, ...
            cfg.grid_dims(1), cfg.grid_dims(2), ...
            cfg.matrix_size, ...
            cfg.oversampling_rate, ...
            cfg.noise_ratio_step1, ...
            cfg.noise_ratio_step2);
    catch ME
        fprintf('%-10s ERROR: %s\n', presets_to_compare{i}, ME.message);
    end
end

%% Test 6: Dual-Resolution System Verification
fprintf('\n--- Test 6: Dual-Resolution System Verification ---\n');

try
    cfg = config('research');
    
    % Verify oversampling calculations
    sim_resolution = 1000;  % Example high-res simulation
    ui_resolution = sim_resolution / cfg.oversampling_rate;
    
    fprintf('✓ Dual-resolution system parameters:\n');
    fprintf('  Simulation resolution: %d pixels\n', sim_resolution);
    fprintf('  UI resolution: %d pixels\n', ui_resolution);
    fprintf('  Oversampling rate: %dx\n', cfg.oversampling_rate);
    fprintf('  Matrix size: %dx%d\n', cfg.matrix_size, cfg.matrix_size);
    
    % Verify coordinate scaling
    high_res_coord = [300, 200];
    low_res_coord = high_res_coord / cfg.oversampling_rate;
    fprintf('  Coordinate scaling: [%.1f, %.1f] → [%.1f, %.1f]\n', ...
        high_res_coord(1), high_res_coord(2), low_res_coord(1), low_res_coord(2));
    
catch ME
    fprintf('✗ Dual-resolution verification failed: %s\n', ME.message);
end

%% Test 7: File Path Configuration
fprintf('\n--- Test 7: File Path Configuration ---\n');

try
    cfg = config('default');
    
    fprintf('✓ File path configuration:\n');
    fprintf('  sim_images: %s\n', cfg.paths.sim_images_dir);
    fprintf('  ui_images: %s\n', cfg.paths.ui_images_dir);
    fprintf('  bsf_data: %s\n', cfg.paths.bsf_data_dir);
    fprintf('  output: %s\n', cfg.paths.output_dir);
    
    fprintf('  Default files:\n');
    fprintf('    UI image: %s\n', cfg.files.default_ui_image);
    fprintf('    Kernel map: %s\n', cfg.files.kernel_map_output);
    fprintf('    Output image: %s\n', cfg.files.precompensated_output);
    
catch ME
    fprintf('✗ File path configuration failed: %s\n', ME.message);
end

%% Test Summary
fprintf('\n=== Configuration System Test Summary ===\n');
fprintf('The unified configuration system provides:\n');
fprintf('✓ Centralized parameter management\n');
fprintf('✓ Multiple preset configurations\n');
fprintf('✓ Custom parameter support\n');
fprintf('✓ Automatic parameter validation\n');
fprintf('✓ Dual-resolution system support\n');
fprintf('✓ Consistent file path management\n');

fprintf('\nUsage Examples:\n');
fprintf('  cfg = config();                    %% Default configuration\n');
fprintf('  cfg = config(''research'');          %% Research preset\n');
fprintf('  cfg = config(''custom'', params);    %% Custom parameters\n');
fprintf('  main_workflow_with_config(cfg);    %% Use with main workflow\n');

fprintf('\nConfiguration system is ready for use!\n');
fprintf('=== Test Complete ===\n');
