% TEST_COORDINATE_FIX - Test the coordinate configuration fix

clear; clc;

fprintf('=== Testing Coordinate Configuration Fix ===\n');

% Test the coordinate file that was causing issues
coord_file = 'image_data/processed_image_coordinates.txt';

if exist(coord_file, 'file')
    fprintf('Testing coordinate file: %s\n', coord_file);
    
    % Display file contents
    fprintf('\nFile contents:\n');
    type(coord_file);
    
    try
        % Test the fixed auto_coordinate_config function
        fprintf('\nTesting auto_coordinate_config...\n');
        coords = auto_coordinate_config(coord_file, 'single');
        
        fprintf('✓ Coordinate configuration successful!\n');
        fprintf('Generated coordinates: [%.2f, %.2f]\n', coords(1), coords(2));
        
    catch ME
        fprintf('✗ Coordinate configuration failed: %s\n', ME.message);
        
        % Try manual debugging
        fprintf('\nTrying manual file reading...\n');
        try
            % Test readmatrix directly
            data = readmatrix(coord_file, 'CommentStyle', '%');
            fprintf('readmatrix result size: %dx%d\n', size(data, 1), size(data, 2));
            fprintf('readmatrix result: %s\n', mat2str(data));
            
        catch ME2
            fprintf('readmatrix failed: %s\n', ME2.message);
            
            % Try manual parsing
            fprintf('Trying manual parsing...\n');
            fid = fopen(coord_file, 'r');
            if fid ~= -1
                line_count = 0;
                while ~feof(fid)
                    line = fgetl(fid);
                    line_count = line_count + 1;
                    if ischar(line)
                        fprintf('Line %d: "%s"\n', line_count, line);
                        if ~startsWith(strtrim(line), '%') && ~isempty(strtrim(line))
                            nums = str2num(line); %#ok<ST2NM>
                            fprintf('  Parsed numbers: %s\n', mat2str(nums));
                        end
                    end
                end
                fclose(fid);
            end
        end
    end
    
else
    fprintf('Coordinate file not found: %s\n', coord_file);
    fprintf('Please run simplified_workflow first to generate the coordinate file.\n');
end

fprintf('\n=== Test Complete ===\n');
