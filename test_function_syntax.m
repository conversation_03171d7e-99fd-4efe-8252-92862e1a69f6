% Simple test to verify function syntax
clear; clc;

fprintf('=== Function Syntax Test ===\n');
fprintf('Testing if offline_calculate_all_prekernels.m has valid syntax...\n');

% Check current working directory
fprintf('Current working directory: %s\n', pwd);

% List all .m files
fprintf('Available .m files:\n');
m_files = dir('*.m');
for i = 1:length(m_files)
    fprintf('  %s\n', m_files(i).name);
end

% Try to get function help
fprintf('\nTrying to get function help...\n');
try
    help offline_stage_auto
    fprintf('Function help retrieved successfully.\n');
catch ME
    fprintf('ERROR getting function help: %s\n', ME.message);
end

% Check if function can be found by MATLAB
fprintf('\nChecking function existence...\n');
func_type = exist('offline_stage_auto', 'file');
switch func_type
    case 0
        fprintf('Function not found.\n');
    case 2
        fprintf('Function found as M-file.\n');
    case 3
        fprintf('Function found as MEX-file.\n');
    case 6
        fprintf('Function found as P-file.\n');
    otherwise
        fprintf('Function found with type: %d\n', func_type);
end

% Try a simple function call with minimal parameters
fprintf('\nTesting with simple parameters...\n');
try
    % Create a simple test - just try to call with wrong number of arguments
    % This should give us a different error if the function is found
    offline_stage_auto();
catch ME
    fprintf('Error message: %s\n', ME.message);
    fprintf('Error identifier: %s\n', ME.identifier);
    
    % Check if this is a parameter error (good) or function not found error (bad)
    if contains(ME.message, '输入参数不够') || contains(ME.message, 'Not enough input arguments') || contains(ME.message, 'Too few input arguments')
        fprintf('SUCCESS: Function exists but needs proper parameters.\n');
    elseif contains(ME.message, '未定义') || contains(ME.message, 'Undefined')
        fprintf('PROBLEM: Function is not properly defined or accessible.\n');
    else
        fprintf('UNKNOWN ERROR TYPE.\n');
    end
end

fprintf('\n=== End Test ===\n');
